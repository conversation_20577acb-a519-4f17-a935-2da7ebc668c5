const mysql = require('mysql2/promise');

async function checkUsernames() {
  const connection = await mysql.createConnection({
    host: '************',
    user: 'sysdb',
    password: 'Roskfl2023',
    database: 'xiaoaiPlus'
  });
  
  try {
    const [users] = await connection.execute('SELECT id, username FROM users');
    console.log('现有用户名:');
    users.forEach(user => {
      console.log(`  ID: ${user.id}, Username: ${user.username}`);
    });
  } catch (error) {
    console.error('查询失败:', error.message);
  } finally {
    await connection.end();
  }
}

checkUsernames();
