"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 检查本地存储的用户信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": async ()=>{\n                    try {\n                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get('token');\n                        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get('user');\n                        if (token && savedUser) {\n                            const userData = JSON.parse(savedUser);\n                            setUser(userData);\n                            // 验证token是否仍然有效\n                            try {\n                                await refreshUser();\n                            } catch (error) {\n                                // Token无效，清除本地存储\n                                logout();\n                            }\n                        }\n                    } catch (error) {\n                        console.error('初始化认证状态失败:', error);\n                        logout();\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (username, password)=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.userAPI.login(username, password);\n            if (response.token && response.user) {\n                // 保存token和用户信息\n                js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set('token', response.token, {\n                    expires: 1\n                }); // 1天过期\n                js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set('user', JSON.stringify(response.user), {\n                    expires: 1\n                });\n                setUser(response.user);\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success('登录成功');\n                return true;\n            }\n            return false;\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('登录失败:', error);\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '登录失败，请检查用户ID和密码';\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(errorMessage);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove('token');\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove('user');\n        setUser(null);\n        _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].info('已退出登录');\n    };\n    const refreshUser = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.userAPI.getProfile();\n            if (response.user) {\n                setUser(response.user);\n                js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set('user', JSON.stringify(response.user), {\n                    expires: 1\n                });\n            }\n        } catch (error) {\n            console.error('刷新用户信息失败:', error);\n            throw error;\n        }\n    };\n    const isAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'admin';\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        refreshUser,\n        isAdmin\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

});