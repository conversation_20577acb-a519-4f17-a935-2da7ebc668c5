"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/UserManagement.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/UserManagement.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/HistoryOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/StopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _TokenUsageModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenUsageModal */ \"(app-pages-browser)/./src/components/admin/TokenUsageModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Title, Text } = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Option } = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction UserManagement() {\n    _s();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 1,\n        pageSize: 20,\n        total: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 模态框状态\n    const [createModalVisible, setCreateModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rechargeModalVisible, setRechargeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tokenUsageModalVisible, setTokenUsageModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表单实例\n    const [createForm] = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [rechargeForm] = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    // 用户名检查状态\n    const [usernameCheckStatus, setUsernameCheckStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [usernameCheckMessage, setUsernameCheckMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 加载用户列表\n    const loadUsers = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, pageSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.getAllUsers(page, pageSize);\n            setUsers(response.users);\n            setPagination({\n                current: page,\n                pageSize,\n                total: response.pagination.total\n            });\n        } catch (error) {\n            console.error('加载用户列表失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('加载用户列表失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载用户统计\n    const loadStats = async ()=>{\n        try {\n            // 计算统计信息\n            const totalUsers = users.length;\n            const activeUsers = users.filter((u)=>u.status === 'active').length;\n            const adminUsers = users.filter((u)=>u.role === 'admin').length;\n            const totalTokens = users.reduce((sum, u)=>sum + u.tokens, 0);\n            const avgTokens = totalUsers > 0 ? totalTokens / totalUsers : 0;\n            setStats({\n                total_users: totalUsers,\n                active_users: activeUsers,\n                admin_users: adminUsers,\n                total_tokens: totalTokens,\n                avg_tokens: avgTokens\n            });\n        } catch (error) {\n            console.error('加载统计信息失败:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserManagement.useEffect\": ()=>{\n            loadUsers();\n        }\n    }[\"UserManagement.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserManagement.useEffect\": ()=>{\n            if (users.length > 0) {\n                loadStats();\n            }\n        }\n    }[\"UserManagement.useEffect\"], [\n        users\n    ]);\n    // 检查用户名可用性\n    const checkUsernameAvailability = async (username)=>{\n        if (!username || username.length < 3) {\n            setUsernameCheckStatus('');\n            setUsernameCheckMessage('');\n            return;\n        }\n        setUsernameCheckStatus('validating');\n        setUsernameCheckMessage('检查中...');\n        try {\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.checkUsernameAvailability(username);\n            if (result.available) {\n                setUsernameCheckStatus('success');\n                setUsernameCheckMessage('用户名可用');\n            } else {\n                setUsernameCheckStatus('error');\n                setUsernameCheckMessage(result.message || '用户名不可用');\n            }\n        } catch (error) {\n            setUsernameCheckStatus('error');\n            setUsernameCheckMessage('检查失败，请重试');\n        }\n    };\n    // 创建用户\n    const handleCreateUser = async (values)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.createUser(values);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('用户创建成功');\n            setCreateModalVisible(false);\n            createForm.resetFields();\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            console.error('创建用户失败:', error);\n            if (error.response && error.response.status === 409) {\n                var _error_response_data;\n                _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '用户名已存在，请更换用户名');\n            } else {\n                var _error_response_data1, _error_response;\n                _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data1 = _error_response.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || '创建用户失败');\n            }\n        }\n    };\n    // 充值令牌\n    const handleRecharge = async (values)=>{\n        if (!selectedUser) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.rechargeTokens(selectedUser.id, values.amount, values.description);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('充值成功');\n            setRechargeModalVisible(false);\n            rechargeForm.resetFields();\n            setSelectedUser(null);\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('充值失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '充值失败');\n        }\n    };\n    // 更新用户状态\n    const handleUpdateStatus = async (userId, status)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.updateUserStatus(userId, status);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('用户状态更新成功');\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('更新用户状态失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '更新用户状态失败');\n        }\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: '用户ID',\n            dataIndex: 'id',\n            key: 'id',\n            width: 100,\n            render: (id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                    code: true,\n                    children: id\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '用户名',\n            dataIndex: 'username',\n            key: 'username',\n            width: 150\n        },\n        {\n            title: '角色',\n            dataIndex: 'role',\n            key: 'role',\n            width: 100,\n            render: (role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    color: role === 'admin' ? 'red' : 'blue',\n                    children: role === 'admin' ? '管理员' : '普通用户'\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '令牌余额',\n            dataIndex: 'tokens',\n            key: 'tokens',\n            width: 120,\n            render: (tokens)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    count: tokens,\n                    showZero: true,\n                    color: \"green\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '状态',\n            dataIndex: 'status',\n            key: 'status',\n            width: 100,\n            render: (status)=>{\n                const statusConfig = {\n                    active: {\n                        color: 'success',\n                        text: '正常'\n                    },\n                    inactive: {\n                        color: 'warning',\n                        text: '停用'\n                    },\n                    banned: {\n                        color: 'error',\n                        text: '封禁'\n                    }\n                };\n                const config = statusConfig[status];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            title: '创建时间',\n            dataIndex: 'created_at',\n            key: 'created_at',\n            width: 180,\n            render: (date)=>new Date(date).toLocaleString('zh-CN')\n        },\n        {\n            title: '最后登录',\n            dataIndex: 'last_login',\n            key: 'last_login',\n            width: 180,\n            render: (date)=>date ? new Date(date).toLocaleString('zh-CN') : '从未登录'\n        },\n        {\n            title: '操作',\n            key: 'actions',\n            width: 250,\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            title: \"查看令牌记录\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>{\n                                    setSelectedUser(record);\n                                    setTokenUsageModalVisible(true);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            title: \"充值令牌\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                type: \"primary\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>{\n                                    setSelectedUser(record);\n                                    setRechargeModalVisible(true);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        record.status === 'active' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要停用此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'inactive'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"停用用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 44\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要激活此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'active'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"激活用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 44\n                                    }, void 0),\n                                    type: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要封禁此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'banned'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"封禁用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 49\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"用户管理\"\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"总用户数\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.total_users) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 23\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"活跃用户\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.active_users) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#3f8600'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"管理员\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.admin_users) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#cf1322'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"总令牌数\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.total_tokens) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#1890ff'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 flex justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            type: \"primary\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>setCreateModalVisible(true),\n                            children: \"创建用户\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>loadUsers(pagination.current, pagination.pageSize),\n                            children: \"刷新\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 383,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                columns: columns,\n                dataSource: users,\n                rowKey: \"id\",\n                loading: loading,\n                pagination: {\n                    ...pagination,\n                    showSizeChanger: true,\n                    showQuickJumper: true,\n                    showTotal: (total, range)=>\"第 \".concat(range[0], \"-\").concat(range[1], \" 条，共 \").concat(total, \" 条\")\n                },\n                onChange: (paginationInfo)=>{\n                    loadUsers(paginationInfo.current, paginationInfo.pageSize);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 402,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                title: \"创建新用户\",\n                open: createModalVisible,\n                onCancel: ()=>{\n                    setCreateModalVisible(false);\n                    createForm.resetFields();\n                },\n                footer: null,\n                width: 500,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    form: createForm,\n                    layout: \"vertical\",\n                    onFinish: handleCreateUser,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"用户名\",\n                            name: \"username\",\n                            validateStatus: usernameCheckStatus,\n                            help: usernameCheckMessage,\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入用户名'\n                                },\n                                {\n                                    min: 2,\n                                    message: '用户名至少2个字符'\n                                },\n                                {\n                                    max: 20,\n                                    message: '用户名最多20个字符'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                placeholder: \"请输入用户名\",\n                                onChange: (e)=>{\n                                    const value = e.target.value;\n                                    if (value.length >= 2) {\n                                        // 防抖处理\n                                        setTimeout(()=>{\n                                            if (createForm.getFieldValue('username') === value) {\n                                                checkUsernameAvailability(value);\n                                            }\n                                        }, 500);\n                                    } else {\n                                        setUsernameCheckStatus('');\n                                        setUsernameCheckMessage('');\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"密码\",\n                            name: \"password\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入密码'\n                                },\n                                {\n                                    min: 6,\n                                    message: '密码至少6个字符'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"].Password, {\n                                placeholder: \"请输入密码\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"初始令牌数\",\n                            name: \"tokens\",\n                            initialValue: 1000,\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入初始令牌数'\n                                },\n                                {\n                                    type: 'number',\n                                    min: 0,\n                                    message: '令牌数不能为负数'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                placeholder: \"请输入初始令牌数\",\n                                style: {\n                                    width: '100%'\n                                },\n                                min: 0,\n                                max: 999999\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            className: \"mb-0 text-right\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        onClick: ()=>{\n                                            setCreateModalVisible(false);\n                                            createForm.resetFields();\n                                        },\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        type: \"primary\",\n                                        htmlType: \"submit\",\n                                        children: \"创建\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                title: \"为用户 \".concat(selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.username, \" 充值令牌\"),\n                open: rechargeModalVisible,\n                onCancel: ()=>{\n                    setRechargeModalVisible(false);\n                    rechargeForm.resetFields();\n                    setSelectedUser(null);\n                },\n                footer: null,\n                width: 500,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            type: \"secondary\",\n                            children: [\n                                \"当前余额: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    children: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.tokens) || 0\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 19\n                                }, this),\n                                \" 令牌\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        form: rechargeForm,\n                        layout: \"vertical\",\n                        onFinish: handleRecharge,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                label: \"充值数量\",\n                                name: \"amount\",\n                                rules: [\n                                    {\n                                        required: true,\n                                        message: '请输入充值数量'\n                                    },\n                                    {\n                                        type: 'number',\n                                        min: 1,\n                                        message: '充值数量至少为1'\n                                    }\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    placeholder: \"请输入充值数量\",\n                                    style: {\n                                        width: '100%'\n                                    },\n                                    min: 1,\n                                    max: 999999\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                label: \"充值说明\",\n                                name: \"description\",\n                                initialValue: \"管理员充值\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"].TextArea, {\n                                    placeholder: \"请输入充值说明（可选）\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                className: \"mb-0 text-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            onClick: ()=>{\n                                                setRechargeModalVisible(false);\n                                                rechargeForm.resetFields();\n                                                setSelectedUser(null);\n                                            },\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            type: \"primary\",\n                                            htmlType: \"submit\",\n                                            children: \"确认充值\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 509,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenUsageModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                visible: tokenUsageModalVisible,\n                onClose: ()=>{\n                    setTokenUsageModalVisible(false);\n                    setSelectedUser(null);\n                },\n                userId: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.id) || '',\n                username: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.username) || ''\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 576,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, this);\n}\n_s(UserManagement, \"I/sukycfvf8mdqQ6L2WswooZtBI=\", false, function() {\n    return [\n        _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm,\n        _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm\n    ];\n});\n_c = UserManagement;\nvar _c;\n$RefreshReg$(_c, \"UserManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/UserManagement.tsx\n"));

/***/ })

});