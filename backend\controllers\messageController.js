const Message = require('../models/Message');
const Conversation = require('../models/Conversation');
const User = require('../models/User');
const ollamaService = require('../services/ollamaService');

// 发送消息并获取AI回复
const sendMessage = async (req, res) => {
  try {
    const { conversationId, content } = req.body;
    const userId = req.user.id;

    if (!conversationId || !content || content.trim().length === 0) {
      return res.status(400).json({
        error: '参数错误',
        message: '对话ID和消息内容不能为空'
      });
    }

    // 检查对话是否存在且属于当前用户
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return res.status(404).json({
        error: '对话不存在',
        message: '找不到指定的对话'
      });
    }

    if (conversation.user_id !== userId) {
      return res.status(403).json({
        error: '权限不足',
        message: '您只能在自己的对话中发送消息'
      });
    }

    // 检查用户令牌余额
    const user = await User.findById(userId);
    const tokensPerMessage = parseInt(process.env.TOKENS_PER_MESSAGE) || 10;
    
    if (user.tokens < tokensPerMessage) {
      return res.status(402).json({
        error: '令牌不足',
        message: '您的令牌余额不足，无法发送消息',
        requiredTokens: tokensPerMessage,
        currentTokens: user.tokens
      });
    }

    // 保存用户消息
    const userMessage = await Message.create({
      conversationId,
      userId,
      role: 'user',
      content: content.trim(),
      tokensUsed: 0
    });

    // 获取对话历史（最近10条消息）
    const messagesResult = await Message.getByConversationId(conversationId, 1, 10);
    const conversationHistory = messagesResult.messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    // 生成AI回复
    const aiResponse = await ollamaService.generateResponse(conversationHistory);

    // 保存AI回复
    const aiMessage = await Message.create({
      conversationId,
      userId,
      role: 'assistant',
      content: aiResponse.content,
      tokensUsed: aiResponse.tokensUsed
    });

    // 扣除用户令牌
    const totalTokensUsed = tokensPerMessage + aiResponse.tokensUsed;
    await User.updateTokens(
      userId,
      totalTokensUsed,
      'consume',
      `对话消息消耗 - 对话ID: ${conversationId}`
    );

    // 更新对话的最后活动时间
    await Conversation.updateLastActivity(conversationId);

    // 如果这是对话的第一条消息，自动生成标题
    if (messagesResult.pagination.total <= 2) {
      try {
        const generatedTitle = await ollamaService.generateTitle(content.trim());
        await Conversation.updateTitle(conversationId, generatedTitle);
      } catch (error) {
        console.error('生成对话标题失败:', error);
      }
    }

    res.json({
      message: '消息发送成功',
      userMessage,
      aiMessage,
      tokensUsed: totalTokensUsed,
      remainingTokens: user.tokens - totalTokensUsed
    });
  } catch (error) {
    console.error('发送消息错误:', error);
    
    if (error.message.includes('AI服务')) {
      return res.status(503).json({
        error: 'AI服务不可用',
        message: error.message
      });
    }

    res.status(500).json({
      error: '服务器错误',
      message: '发送消息时发生错误'
    });
  }
};

// 流式发送消息（实时响应）
const sendMessageStream = async (req, res) => {
  try {
    const { conversationId, content } = req.body;
    const userId = req.user.id;

    if (!conversationId || !content || content.trim().length === 0) {
      return res.status(400).json({
        error: '参数错误',
        message: '对话ID和消息内容不能为空'
      });
    }

    // 检查对话权限和令牌余额（与上面相同的逻辑）
    const conversation = await Conversation.findById(conversationId);
    if (!conversation || conversation.user_id !== userId) {
      return res.status(403).json({
        error: '权限不足',
        message: '无法访问该对话'
      });
    }

    const user = await User.findById(userId);
    const tokensPerMessage = parseInt(process.env.TOKENS_PER_MESSAGE) || 10;
    
    if (user.tokens < tokensPerMessage) {
      return res.status(402).json({
        error: '令牌不足',
        message: '您的令牌余额不足，无法发送消息'
      });
    }

    // 设置SSE响应头
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // 保存用户消息
    const userMessage = await Message.create({
      conversationId,
      userId,
      role: 'user',
      content: content.trim(),
      tokensUsed: 0
    });

    // 发送用户消息确认
    res.write(`data: ${JSON.stringify({
      type: 'user_message',
      message: userMessage
    })}\n\n`);

    // 获取对话历史
    const messagesResult = await Message.getByConversationId(conversationId, 1, 10);
    const conversationHistory = messagesResult.messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    // 生成流式AI回复
    const stream = await ollamaService.generateStreamResponse(conversationHistory);
    let aiResponseContent = '';

    for await (const chunk of stream) {
      if (chunk.response) {
        aiResponseContent += chunk.response;
        res.write(`data: ${JSON.stringify({
          type: 'ai_chunk',
          content: chunk.response
        })}\n\n`);
      }

      if (chunk.done) {
        break;
      }
    }

    // 保存完整的AI回复
    const tokensUsed = ollamaService.estimateTokens(aiResponseContent);
    const aiMessage = await Message.create({
      conversationId,
      userId,
      role: 'assistant',
      content: aiResponseContent,
      tokensUsed
    });

    // 扣除令牌
    const totalTokensUsed = tokensPerMessage + tokensUsed;
    await User.updateTokens(userId, totalTokensUsed, 'consume', `流式对话消息消耗`);

    // 发送完成信号
    res.write(`data: ${JSON.stringify({
      type: 'completed',
      aiMessage,
      tokensUsed: totalTokensUsed,
      remainingTokens: user.tokens - totalTokensUsed
    })}\n\n`);

    res.end();
  } catch (error) {
    console.error('流式发送消息错误:', error);
    res.write(`data: ${JSON.stringify({
      type: 'error',
      error: '发送消息时发生错误'
    })}\n\n`);
    res.end();
  }
};

// 获取消息列表
const getMessages = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;

    // 检查对话权限
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return res.status(404).json({
        error: '对话不存在',
        message: '找不到指定的对话'
      });
    }

    if (req.user.role !== 'admin' && conversation.user_id !== userId) {
      return res.status(403).json({
        error: '权限不足',
        message: '您只能查看自己的消息'
      });
    }

    const result = await Message.getByConversationId(conversationId, page, limit);

    res.json({
      message: '获取消息列表成功',
      ...result
    });
  } catch (error) {
    console.error('获取消息列表错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '获取消息列表时发生错误'
    });
  }
};

module.exports = {
  sendMessage,
  sendMessageStream,
  getMessages
};
