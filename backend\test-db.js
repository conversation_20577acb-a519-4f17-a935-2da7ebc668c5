const mysql = require('mysql2/promise');
require('dotenv').config();

async function testConnection() {
  try {
    console.log('🔄 测试数据库连接...');
    console.log('配置信息:');
    console.log(`  主机: ${process.env.DB_HOST || 'localhost'}`);
    console.log(`  端口: ${process.env.DB_PORT || 3306}`);
    console.log(`  用户: ${process.env.DB_USER || 'root'}`);
    console.log(`  数据库: ${process.env.DB_NAME || 'xap_system'}`);

    // 首先尝试连接到MySQL服务器（不指定数据库）
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
    });

    console.log('✅ 成功连接到MySQL服务器');

    // 检查数据库是否存在
    const dbName = process.env.DB_NAME || 'xap_system';
    const [databases] = await connection.execute('SHOW DATABASES');
    const dbExists = databases.some(db => Object.values(db)[0] === dbName);
    
    if (dbExists) {
      console.log(`✅ 数据库 ${dbName} 已存在`);
    } else {
      console.log(`⚠️  数据库 ${dbName} 不存在，需要创建`);
    }

    await connection.end();
    console.log('🎉 数据库连接测试完成');

  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.error('请检查:');
    console.error('1. MySQL服务是否正在运行');
    console.error('2. 用户名和密码是否正确');
    console.error('3. 主机和端口是否正确');
  }
}

testConnection();
