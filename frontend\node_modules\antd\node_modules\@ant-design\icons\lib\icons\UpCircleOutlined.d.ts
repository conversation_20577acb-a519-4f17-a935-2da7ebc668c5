import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![up-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxOC41IDM2MC4zYTcuOTUgNy45NSAwIDAwLTEyLjkgMGwtMTc4IDI0NmMtMy44IDUuMyAwIDEyLjcgNi41IDEyLjdIMzgxYzEwLjIgMCAxOS45LTQuOSAyNS45LTEzLjJMNTEyIDQ2MC40bDEwNS4yIDE0NS40YzYgOC4zIDE1LjYgMTMuMiAyNS45IDEzLjJINjkwYzYuNSAwIDEwLjMtNy40IDYuNS0xMi43bC0xNzgtMjQ2eiIgLz48cGF0aCBkPSJNNTEyIDY0QzI2NC42IDY0IDY0IDI2NC42IDY0IDUxMnMyMDAuNiA0NDggNDQ4IDQ0OCA0NDgtMjAwLjYgNDQ4LTQ0OFM3NTkuNCA2NCA1MTIgNjR6bTAgODIwYy0yMDUuNCAwLTM3Mi0xNjYuNi0zNzItMzcyczE2Ni42LTM3MiAzNzItMzcyIDM3MiAxNjYuNiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzIgMzcyeiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
