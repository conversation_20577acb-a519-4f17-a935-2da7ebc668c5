"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/UserManagement.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/UserManagement.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/HistoryOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/StopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _TokenUsageModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenUsageModal */ \"(app-pages-browser)/./src/components/admin/TokenUsageModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Title, Text } = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Option } = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction UserManagement() {\n    _s();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 1,\n        pageSize: 20,\n        total: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 模态框状态\n    const [createModalVisible, setCreateModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rechargeModalVisible, setRechargeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tokenUsageModalVisible, setTokenUsageModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表单实例\n    const [createForm] = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [rechargeForm] = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    // 用户名检查状态\n    const [usernameCheckStatus, setUsernameCheckStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [usernameCheckMessage, setUsernameCheckMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 加载用户列表\n    const loadUsers = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, pageSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.getAllUsers(page, pageSize);\n            setUsers(response.users);\n            setPagination({\n                current: page,\n                pageSize,\n                total: response.pagination.total\n            });\n        } catch (error) {\n            console.error('加载用户列表失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('加载用户列表失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载用户统计\n    const loadStats = async ()=>{\n        try {\n            // 计算统计信息\n            const totalUsers = users.length;\n            const activeUsers = users.filter((u)=>u.status === 'active').length;\n            const adminUsers = users.filter((u)=>u.role === 'admin').length;\n            const totalTokens = users.reduce((sum, u)=>sum + u.tokens, 0);\n            const avgTokens = totalUsers > 0 ? totalTokens / totalUsers : 0;\n            setStats({\n                total_users: totalUsers,\n                active_users: activeUsers,\n                admin_users: adminUsers,\n                total_tokens: totalTokens,\n                avg_tokens: avgTokens\n            });\n        } catch (error) {\n            console.error('加载统计信息失败:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserManagement.useEffect\": ()=>{\n            loadUsers();\n        }\n    }[\"UserManagement.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserManagement.useEffect\": ()=>{\n            if (users.length > 0) {\n                loadStats();\n            }\n        }\n    }[\"UserManagement.useEffect\"], [\n        users\n    ]);\n    // 创建用户\n    const handleCreateUser = async (values)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.createUser(values);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('用户创建成功');\n            setCreateModalVisible(false);\n            createForm.resetFields();\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            console.error('创建用户失败:', error);\n            if (error.response && error.response.status === 409) {\n                var _error_response_data;\n                _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '用户名已存在，请更换用户名');\n            } else {\n                var _error_response_data1, _error_response;\n                _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data1 = _error_response.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || '创建用户失败');\n            }\n        }\n    };\n    // 充值令牌\n    const handleRecharge = async (values)=>{\n        if (!selectedUser) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.rechargeTokens(selectedUser.id, values.amount, values.description);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('充值成功');\n            setRechargeModalVisible(false);\n            rechargeForm.resetFields();\n            setSelectedUser(null);\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('充值失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '充值失败');\n        }\n    };\n    // 更新用户状态\n    const handleUpdateStatus = async (userId, status)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.updateUserStatus(userId, status);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('用户状态更新成功');\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('更新用户状态失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '更新用户状态失败');\n        }\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: '用户ID',\n            dataIndex: 'id',\n            key: 'id',\n            width: 100,\n            render: (id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                    code: true,\n                    children: id\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '用户名',\n            dataIndex: 'username',\n            key: 'username',\n            width: 150\n        },\n        {\n            title: '角色',\n            dataIndex: 'role',\n            key: 'role',\n            width: 100,\n            render: (role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    color: role === 'admin' ? 'red' : 'blue',\n                    children: role === 'admin' ? '管理员' : '普通用户'\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '令牌余额',\n            dataIndex: 'tokens',\n            key: 'tokens',\n            width: 120,\n            render: (tokens)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    count: tokens,\n                    showZero: true,\n                    color: \"green\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '状态',\n            dataIndex: 'status',\n            key: 'status',\n            width: 100,\n            render: (status)=>{\n                const statusConfig = {\n                    active: {\n                        color: 'success',\n                        text: '正常'\n                    },\n                    inactive: {\n                        color: 'warning',\n                        text: '停用'\n                    },\n                    banned: {\n                        color: 'error',\n                        text: '封禁'\n                    }\n                };\n                const config = statusConfig[status];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            title: '创建时间',\n            dataIndex: 'created_at',\n            key: 'created_at',\n            width: 180,\n            render: (date)=>new Date(date).toLocaleString('zh-CN')\n        },\n        {\n            title: '最后登录',\n            dataIndex: 'last_login',\n            key: 'last_login',\n            width: 180,\n            render: (date)=>date ? new Date(date).toLocaleString('zh-CN') : '从未登录'\n        },\n        {\n            title: '操作',\n            key: 'actions',\n            width: 250,\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            title: \"查看令牌记录\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>{\n                                    setSelectedUser(record);\n                                    setTokenUsageModalVisible(true);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            title: \"充值令牌\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                type: \"primary\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>{\n                                    setSelectedUser(record);\n                                    setRechargeModalVisible(true);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        record.status === 'active' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要停用此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'inactive'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"停用用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 44\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要激活此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'active'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"激活用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 44\n                                    }, void 0),\n                                    type: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要封禁此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'banned'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"封禁用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 49\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"用户管理\"\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"总用户数\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.total_users) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 23\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"活跃用户\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.active_users) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#3f8600'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"管理员\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.admin_users) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#cf1322'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"总令牌数\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.total_tokens) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#1890ff'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 flex justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            type: \"primary\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>setCreateModalVisible(true),\n                            children: \"创建用户\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>loadUsers(pagination.current, pagination.pageSize),\n                            children: \"刷新\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 358,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                columns: columns,\n                dataSource: users,\n                rowKey: \"id\",\n                loading: loading,\n                pagination: {\n                    ...pagination,\n                    showSizeChanger: true,\n                    showQuickJumper: true,\n                    showTotal: (total, range)=>\"第 \".concat(range[0], \"-\").concat(range[1], \" 条，共 \").concat(total, \" 条\")\n                },\n                onChange: (paginationInfo)=>{\n                    loadUsers(paginationInfo.current, paginationInfo.pageSize);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 376,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                title: \"创建新用户\",\n                open: createModalVisible,\n                onCancel: ()=>{\n                    setCreateModalVisible(false);\n                    createForm.resetFields();\n                },\n                footer: null,\n                width: 500,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    form: createForm,\n                    layout: \"vertical\",\n                    onFinish: handleCreateUser,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"用户名\",\n                            name: \"username\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入用户名'\n                                },\n                                {\n                                    min: 2,\n                                    message: '用户名至少2个字符'\n                                },\n                                {\n                                    max: 20,\n                                    message: '用户名最多20个字符'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                placeholder: \"请输入用户名\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"密码\",\n                            name: \"password\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入密码'\n                                },\n                                {\n                                    min: 6,\n                                    message: '密码至少6个字符'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"].Password, {\n                                placeholder: \"请输入密码\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"初始令牌数\",\n                            name: \"tokens\",\n                            initialValue: 1000,\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入初始令牌数'\n                                },\n                                {\n                                    type: 'number',\n                                    min: 0,\n                                    message: '令牌数不能为负数'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                placeholder: \"请输入初始令牌数\",\n                                style: {\n                                    width: '100%'\n                                },\n                                min: 0,\n                                max: 999999\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            className: \"mb-0 text-right\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        onClick: ()=>{\n                                            setCreateModalVisible(false);\n                                            createForm.resetFields();\n                                        },\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        type: \"primary\",\n                                        htmlType: \"submit\",\n                                        children: \"创建\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 393,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                title: \"为用户 \".concat(selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.username, \" 充值令牌\"),\n                open: rechargeModalVisible,\n                onCancel: ()=>{\n                    setRechargeModalVisible(false);\n                    rechargeForm.resetFields();\n                    setSelectedUser(null);\n                },\n                footer: null,\n                width: 500,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            type: \"secondary\",\n                            children: [\n                                \"当前余额: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    children: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.tokens) || 0\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 19\n                                }, this),\n                                \" 令牌\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 477,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        form: rechargeForm,\n                        layout: \"vertical\",\n                        onFinish: handleRecharge,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                label: \"充值数量\",\n                                name: \"amount\",\n                                rules: [\n                                    {\n                                        required: true,\n                                        message: '请输入充值数量'\n                                    },\n                                    {\n                                        type: 'number',\n                                        min: 1,\n                                        message: '充值数量至少为1'\n                                    }\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    placeholder: \"请输入充值数量\",\n                                    style: {\n                                        width: '100%'\n                                    },\n                                    min: 1,\n                                    max: 999999\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                label: \"充值说明\",\n                                name: \"description\",\n                                initialValue: \"管理员充值\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"].TextArea, {\n                                    placeholder: \"请输入充值说明（可选）\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                className: \"mb-0 text-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            onClick: ()=>{\n                                                setRechargeModalVisible(false);\n                                                rechargeForm.resetFields();\n                                                setSelectedUser(null);\n                                            },\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            type: \"primary\",\n                                            htmlType: \"submit\",\n                                            children: \"确认充值\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 465,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenUsageModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                visible: tokenUsageModalVisible,\n                onClose: ()=>{\n                    setTokenUsageModalVisible(false);\n                    setSelectedUser(null);\n                },\n                userId: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.id) || '',\n                username: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.username) || ''\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 532,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n        lineNumber: 310,\n        columnNumber: 5\n    }, this);\n}\n_s(UserManagement, \"I/sukycfvf8mdqQ6L2WswooZtBI=\", false, function() {\n    return [\n        _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm,\n        _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm\n    ];\n});\n_c = UserManagement;\nvar _c;\n$RefreshReg$(_c, \"UserManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/UserManagement.tsx\n"));

/***/ })

});