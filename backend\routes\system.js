const express = require('express');
const router = express.Router();
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const ollamaService = require('../services/ollamaService');
const Message = require('../models/Message');

// 检查Ollama服务状态
router.get('/ollama/health', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const health = await ollamaService.checkHealth();
    res.json({
      message: 'Ollama服务状态检查完成',
      ...health
    });
  } catch (error) {
    console.error('检查Ollama服务状态错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '检查Ollama服务状态时发生错误'
    });
  }
});

// 获取可用模型列表
router.get('/ollama/models', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const models = await ollamaService.getAvailableModels();
    res.json({
      message: '获取模型列表成功',
      models
    });
  } catch (error) {
    console.error('获取模型列表错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '获取模型列表时发生错误'
    });
  }
});

// 拉取新模型
router.post('/ollama/models/:modelName/pull', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { modelName } = req.params;
    
    // 设置SSE响应头
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    });

    const stream = await ollamaService.pullModel(modelName);

    for await (const chunk of stream) {
      res.write(`data: ${JSON.stringify(chunk)}\n\n`);
      
      if (chunk.status === 'success') {
        break;
      }
    }

    res.end();
  } catch (error) {
    console.error('拉取模型错误:', error);
    res.write(`data: ${JSON.stringify({
      status: 'error',
      error: error.message
    })}\n\n`);
    res.end();
  }
});

// 获取系统统计信息
router.get('/stats', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    // 获取令牌使用统计
    const tokenStats = await Message.getSystemTokenUsageStats(startDate, endDate);
    
    // 获取用户统计
    const { query } = require('../config/database');
    
    const userStatsQuery = `
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
        COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
        AVG(tokens) as avg_tokens,
        SUM(tokens) as total_tokens
      FROM users
    `;
    
    const userStats = await query(userStatsQuery);
    
    // 获取对话统计
    const conversationStatsQuery = `
      SELECT 
        COUNT(*) as total_conversations,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_conversations,
        AVG((SELECT COUNT(*) FROM messages WHERE conversation_id = conversations.id)) as avg_messages_per_conversation
      FROM conversations
    `;
    
    const conversationStats = await query(conversationStatsQuery);

    res.json({
      message: '获取系统统计信息成功',
      stats: {
        users: userStats[0],
        conversations: conversationStats[0],
        tokens: tokenStats
      }
    });
  } catch (error) {
    console.error('获取系统统计信息错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '获取系统统计信息时发生错误'
    });
  }
});

module.exports = router;
