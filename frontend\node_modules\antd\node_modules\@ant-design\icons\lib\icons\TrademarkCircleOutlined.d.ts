import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![trademark-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnptODcuNS0zMzQuN2MzNC44LTEyLjggNzguNC00OSA3OC40LTExOS4yIDAtNzEuMi00NS41LTEzMS4xLTE0NC4yLTEzMS4xSDM3OGMtNC40IDAtOCAzLjYtOCA4djQxMGMwIDQuNCAzLjYgOCA4IDhoNTQuNWM0LjQgMCA4LTMuNiA4LThWNTYxLjJoODguN2w3NC42IDE1OS4yYzEuMyAyLjggNC4xIDQuNiA3LjIgNC42aDYyYTcuOSA3LjkgMCAwMDcuMS0xMS41bC04MC42LTE2NC4yek01MjIgNTA1aC04MS41VjM1N2g4My40YzQ4IDAgODAuOSAyNS4zIDgwLjkgNzUuNSAwIDQ2LjktMjkuOCA3Mi41LTgyLjggNzIuNXoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
