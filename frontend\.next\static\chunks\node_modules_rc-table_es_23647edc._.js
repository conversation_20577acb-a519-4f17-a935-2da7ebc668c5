(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/rc-table/es/constant.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EXPAND_COLUMN": (()=>EXPAND_COLUMN),
    "INTERNAL_HOOKS": (()=>INTERNAL_HOOKS)
});
var EXPAND_COLUMN = {};
var INTERNAL_HOOKS = 'rc-table-internal-hook';
}}),
"[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "makeImmutable": (()=>makeImmutable),
    "responseImmutable": (()=>responseImmutable),
    "useImmutableMark": (()=>useImmutableMark)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$Immutable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__createImmutable$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/Immutable.js [app-client] (ecmascript) <export default as createImmutable>");
;
var _createImmutable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$Immutable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__createImmutable$3e$__["createImmutable"])(), makeImmutable = _createImmutable.makeImmutable, responseImmutable = _createImmutable.responseImmutable, useImmutableMark = _createImmutable.useImmutableMark;
;
var TableContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])();
const __TURBOPACK__default__export__ = TableContext;
}}),
"[project]/node_modules/rc-table/es/hooks/useRenderTimes.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* istanbul ignore file */ __turbopack_context__.s({
    "RenderBlock": (()=>RenderBlock),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function useRenderTimes(props, debug) {
    // Render times
    var timesRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    timesRef.current += 1;
    // Props changed
    var propsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(props);
    var keys = [];
    Object.keys(props || {}).map(function(key) {
        var _propsRef$current;
        if ((props === null || props === void 0 ? void 0 : props[key]) !== ((_propsRef$current = propsRef.current) === null || _propsRef$current === void 0 ? void 0 : _propsRef$current[key])) {
            keys.push(key);
        }
    });
    propsRef.current = props;
    // Cache keys since React rerender may cause it lost
    var keysRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    if (keys.length) {
        keysRef.current = keys;
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDebugValue"])(timesRef.current);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDebugValue"])(keysRef.current.join(', '));
    if (debug) {
        console.log("".concat(debug, ":"), timesRef.current, keysRef.current);
    }
    return timesRef.current;
}
const __TURBOPACK__default__export__ = ("TURBOPACK compile-time truthy", 1) ? useRenderTimes : ("TURBOPACK unreachable", undefined);
var RenderBlock = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(function() {
    var times = useRenderTimes();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("h1", null, "Render Times: ", times);
});
if ("TURBOPACK compile-time truthy", 1) {
    RenderBlock.displayName = 'RenderBlock';
}
}}),
"[project]/node_modules/rc-table/es/context/PerfContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
// TODO: Remove when use `responsiveImmutable`
var PerfContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    renderWithProps: false
});
const __TURBOPACK__default__export__ = PerfContext;
}}),
"[project]/node_modules/rc-table/es/utils/valueUtil.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getColumnsKey": (()=>getColumnsKey),
    "validNumberValue": (()=>validNumberValue),
    "validateValue": (()=>validateValue)
});
var INTERNAL_KEY_PREFIX = 'RC_TABLE_KEY';
function toArray(arr) {
    if (arr === undefined || arr === null) {
        return [];
    }
    return Array.isArray(arr) ? arr : [
        arr
    ];
}
function getColumnsKey(columns) {
    var columnKeys = [];
    var keys = {};
    columns.forEach(function(column) {
        var _ref = column || {}, key = _ref.key, dataIndex = _ref.dataIndex;
        var mergedKey = key || toArray(dataIndex).join('-') || INTERNAL_KEY_PREFIX;
        while(keys[mergedKey]){
            mergedKey = "".concat(mergedKey, "_next");
        }
        keys[mergedKey] = true;
        columnKeys.push(mergedKey);
    });
    return columnKeys;
}
function validateValue(val) {
    return val !== null && val !== undefined;
}
function validNumberValue(value) {
    return typeof value === 'number' && !Number.isNaN(value);
}
}}),
"[project]/node_modules/rc-table/es/Cell/useCellRender.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useCellRender)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMemo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useMemo.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$isEqual$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/isEqual.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$utils$2f$get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/utils/get.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$PerfContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/PerfContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/valueUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
function isRenderCell(data) {
    return data && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(data) === 'object' && !Array.isArray(data) && !/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(data);
}
function useCellRender(record, dataIndex, renderIndex, children, render, shouldCellUpdate) {
    // TODO: Remove this after next major version
    var perfRecord = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$PerfContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
    var mark = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImmutableMark"])();
    // ======================== Render ========================
    var retData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMemo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "useCellRender.useMemo[retData]": function() {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateValue"])(children)) {
                return [
                    children
                ];
            }
            var path = dataIndex === null || dataIndex === undefined || dataIndex === '' ? [] : Array.isArray(dataIndex) ? dataIndex : [
                dataIndex
            ];
            var value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$utils$2f$get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(record, path);
            // Customize render node
            var returnChildNode = value;
            var returnCellProps = undefined;
            if (render) {
                var renderData = render(value, record, renderIndex);
                if (isRenderCell(renderData)) {
                    if ("TURBOPACK compile-time truthy", 1) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, '`columns.render` return cell props is deprecated with perf issue, please use `onCell` instead.');
                    }
                    returnChildNode = renderData.children;
                    returnCellProps = renderData.props;
                    perfRecord.renderWithProps = true;
                } else {
                    returnChildNode = renderData;
                }
            }
            return [
                returnChildNode,
                returnCellProps
            ];
        }
    }["useCellRender.useMemo[retData]"], [
        // Force update deps
        mark,
        // Normal deps
        record,
        children,
        dataIndex,
        render,
        renderIndex
    ], {
        "useCellRender.useMemo[retData]": function(prev, next) {
            if (shouldCellUpdate) {
                var _prev = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prev, 2), prevRecord = _prev[1];
                var _next = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(next, 2), nextRecord = _next[1];
                return shouldCellUpdate(nextRecord, prevRecord);
            }
            // Legacy mode should always update
            if (perfRecord.renderWithProps) {
                return true;
            }
            return !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$isEqual$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prev, next, true);
        }
    }["useCellRender.useMemo[retData]"]);
    return retData;
}
}}),
"[project]/node_modules/rc-table/es/Cell/useHoverState.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useHoverState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
;
;
/** Check if cell is in hover range */ function inHoverRange(cellStartRow, cellRowSpan, startRow, endRow) {
    var cellEndRow = cellStartRow + cellRowSpan - 1;
    return cellStartRow <= endRow && cellEndRow >= startRow;
}
function useHoverState(rowIndex, rowSpan) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        "useHoverState.useContext": function(ctx) {
            var hovering = inHoverRange(rowIndex, rowSpan || 1, ctx.hoverStartRow, ctx.hoverEndRow);
            return [
                hovering,
                ctx.onHover
            ];
        }
    }["useHoverState.useContext"]);
}
}}),
"[project]/node_modules/rc-table/es/Cell/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/defineProperty.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useRenderTimes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$useCellRender$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Cell/useCellRender.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$useHoverState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Cell/useHoverState.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useEvent.js [app-client] (ecmascript) <export default as useEvent>");
;
;
;
;
;
;
;
;
;
;
;
;
;
var getTitleFromCellRenderChildren = function getTitleFromCellRenderChildren(_ref) {
    var ellipsis = _ref.ellipsis, rowType = _ref.rowType, children = _ref.children;
    var title;
    var ellipsisConfig = ellipsis === true ? {
        showTitle: true
    } : ellipsis;
    if (ellipsisConfig && (ellipsisConfig.showTitle || rowType === 'header')) {
        if (typeof children === 'string' || typeof children === 'number') {
            title = children.toString();
        } else if (/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(children) && typeof children.props.children === 'string') {
            title = children.props.children;
        }
    }
    return title;
};
function Cell(props) {
    var _ref2, _ref3, _legacyCellProps$colS, _ref4, _ref5, _legacyCellProps$rowS, _additionalProps$titl, _classNames;
    if ("TURBOPACK compile-time truthy", 1) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props);
    }
    var Component = props.component, children = props.children, ellipsis = props.ellipsis, scope = props.scope, prefixCls = props.prefixCls, className = props.className, align = props.align, record = props.record, render = props.render, dataIndex = props.dataIndex, renderIndex = props.renderIndex, shouldCellUpdate = props.shouldCellUpdate, index = props.index, rowType = props.rowType, colSpan = props.colSpan, rowSpan = props.rowSpan, fixLeft = props.fixLeft, fixRight = props.fixRight, firstFixLeft = props.firstFixLeft, lastFixLeft = props.lastFixLeft, firstFixRight = props.firstFixRight, lastFixRight = props.lastFixRight, appendNode = props.appendNode, _props$additionalProp = props.additionalProps, additionalProps = _props$additionalProp === void 0 ? {} : _props$additionalProp, isSticky = props.isSticky;
    var cellPrefixCls = "".concat(prefixCls, "-cell");
    var _useContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], [
        'supportSticky',
        'allColumnsFixedLeft',
        'rowHoverable'
    ]), supportSticky = _useContext.supportSticky, allColumnsFixedLeft = _useContext.allColumnsFixedLeft, rowHoverable = _useContext.rowHoverable;
    // ====================== Value =======================
    var _useCellRender = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$useCellRender$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(record, dataIndex, renderIndex, children, render, shouldCellUpdate), _useCellRender2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_useCellRender, 2), childNode = _useCellRender2[0], legacyCellProps = _useCellRender2[1];
    // ====================== Fixed =======================
    var fixedStyle = {};
    var isFixLeft = typeof fixLeft === 'number' && supportSticky;
    var isFixRight = typeof fixRight === 'number' && supportSticky;
    if (isFixLeft) {
        fixedStyle.position = 'sticky';
        fixedStyle.left = fixLeft;
    }
    if (isFixRight) {
        fixedStyle.position = 'sticky';
        fixedStyle.right = fixRight;
    }
    // ================ RowSpan & ColSpan =================
    var mergedColSpan = (_ref2 = (_ref3 = (_legacyCellProps$colS = legacyCellProps === null || legacyCellProps === void 0 ? void 0 : legacyCellProps.colSpan) !== null && _legacyCellProps$colS !== void 0 ? _legacyCellProps$colS : additionalProps.colSpan) !== null && _ref3 !== void 0 ? _ref3 : colSpan) !== null && _ref2 !== void 0 ? _ref2 : 1;
    var mergedRowSpan = (_ref4 = (_ref5 = (_legacyCellProps$rowS = legacyCellProps === null || legacyCellProps === void 0 ? void 0 : legacyCellProps.rowSpan) !== null && _legacyCellProps$rowS !== void 0 ? _legacyCellProps$rowS : additionalProps.rowSpan) !== null && _ref5 !== void 0 ? _ref5 : rowSpan) !== null && _ref4 !== void 0 ? _ref4 : 1;
    // ====================== Hover =======================
    var _useHoverState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$useHoverState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(index, mergedRowSpan), _useHoverState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_useHoverState, 2), hovering = _useHoverState2[0], onHover = _useHoverState2[1];
    var onMouseEnter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__["useEvent"])({
        "Cell.useEvent[onMouseEnter]": function(event) {
            var _additionalProps$onMo;
            if (record) {
                onHover(index, index + mergedRowSpan - 1);
            }
            additionalProps === null || additionalProps === void 0 || (_additionalProps$onMo = additionalProps.onMouseEnter) === null || _additionalProps$onMo === void 0 || _additionalProps$onMo.call(additionalProps, event);
        }
    }["Cell.useEvent[onMouseEnter]"]);
    var onMouseLeave = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__["useEvent"])({
        "Cell.useEvent[onMouseLeave]": function(event) {
            var _additionalProps$onMo2;
            if (record) {
                onHover(-1, -1);
            }
            additionalProps === null || additionalProps === void 0 || (_additionalProps$onMo2 = additionalProps.onMouseLeave) === null || _additionalProps$onMo2 === void 0 || _additionalProps$onMo2.call(additionalProps, event);
        }
    }["Cell.useEvent[onMouseLeave]"]);
    // ====================== Render ======================
    if (mergedColSpan === 0 || mergedRowSpan === 0) {
        return null;
    }
    // >>>>> Title
    var title = (_additionalProps$titl = additionalProps.title) !== null && _additionalProps$titl !== void 0 ? _additionalProps$titl : getTitleFromCellRenderChildren({
        rowType: rowType,
        ellipsis: ellipsis,
        children: childNode
    });
    // >>>>> ClassName
    var mergedClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(cellPrefixCls, className, (_classNames = {}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_classNames, "".concat(cellPrefixCls, "-fix-left"), isFixLeft && supportSticky), "".concat(cellPrefixCls, "-fix-left-first"), firstFixLeft && supportSticky), "".concat(cellPrefixCls, "-fix-left-last"), lastFixLeft && supportSticky), "".concat(cellPrefixCls, "-fix-left-all"), lastFixLeft && allColumnsFixedLeft && supportSticky), "".concat(cellPrefixCls, "-fix-right"), isFixRight && supportSticky), "".concat(cellPrefixCls, "-fix-right-first"), firstFixRight && supportSticky), "".concat(cellPrefixCls, "-fix-right-last"), lastFixRight && supportSticky), "".concat(cellPrefixCls, "-ellipsis"), ellipsis), "".concat(cellPrefixCls, "-with-append"), appendNode), "".concat(cellPrefixCls, "-fix-sticky"), (isFixLeft || isFixRight) && isSticky && supportSticky), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_classNames, "".concat(cellPrefixCls, "-row-hover"), !legacyCellProps && hovering)), additionalProps.className, legacyCellProps === null || legacyCellProps === void 0 ? void 0 : legacyCellProps.className);
    // >>>>> Style
    var alignStyle = {};
    if (align) {
        alignStyle.textAlign = align;
    }
    // The order is important since user can overwrite style.
    // For example ant-design/ant-design#51763
    var mergedStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, legacyCellProps === null || legacyCellProps === void 0 ? void 0 : legacyCellProps.style), fixedStyle), alignStyle), additionalProps.style);
    // >>>>> Children Node
    var mergedChildNode = childNode;
    // Not crash if final `childNode` is not validate ReactNode
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(mergedChildNode) === 'object' && !Array.isArray(mergedChildNode) && !/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(mergedChildNode)) {
        mergedChildNode = null;
    }
    if (ellipsis && (lastFixLeft || firstFixRight)) {
        mergedChildNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
            className: "".concat(cellPrefixCls, "-content")
        }, mergedChildNode);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(Component, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, legacyCellProps, additionalProps, {
        className: mergedClassName,
        style: mergedStyle,
        title: title,
        scope: scope,
        onMouseEnter: rowHoverable ? onMouseEnter : undefined,
        onMouseLeave: rowHoverable ? onMouseLeave : undefined,
        colSpan: mergedColSpan !== 1 ? mergedColSpan : null,
        rowSpan: mergedRowSpan !== 1 ? mergedRowSpan : null
    }), appendNode, mergedChildNode);
}
const __TURBOPACK__default__export__ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(Cell);
}}),
"[project]/node_modules/rc-table/es/utils/fixUtil.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getCellFixedInfo": (()=>getCellFixedInfo)
});
function getCellFixedInfo(colStart, colEnd, columns, stickyOffsets, direction) {
    var startColumn = columns[colStart] || {};
    var endColumn = columns[colEnd] || {};
    var fixLeft;
    var fixRight;
    if (startColumn.fixed === 'left') {
        fixLeft = stickyOffsets.left[direction === 'rtl' ? colEnd : colStart];
    } else if (endColumn.fixed === 'right') {
        fixRight = stickyOffsets.right[direction === 'rtl' ? colStart : colEnd];
    }
    var lastFixLeft = false;
    var firstFixRight = false;
    var lastFixRight = false;
    var firstFixLeft = false;
    var nextColumn = columns[colEnd + 1];
    var prevColumn = columns[colStart - 1];
    // need show shadow only when canLastFix is true
    var canLastFix = nextColumn && !nextColumn.fixed || prevColumn && !prevColumn.fixed || columns.every(function(col) {
        return col.fixed === 'left';
    });
    if (direction === 'rtl') {
        if (fixLeft !== undefined) {
            var prevFixLeft = prevColumn && prevColumn.fixed === 'left';
            firstFixLeft = !prevFixLeft && canLastFix;
        } else if (fixRight !== undefined) {
            var nextFixRight = nextColumn && nextColumn.fixed === 'right';
            lastFixRight = !nextFixRight && canLastFix;
        }
    } else if (fixLeft !== undefined) {
        var nextFixLeft = nextColumn && nextColumn.fixed === 'left';
        lastFixLeft = !nextFixLeft && canLastFix;
    } else if (fixRight !== undefined) {
        var prevFixRight = prevColumn && prevColumn.fixed === 'right';
        firstFixRight = !prevFixRight && canLastFix;
    }
    return {
        fixLeft: fixLeft,
        fixRight: fixRight,
        lastFixLeft: lastFixLeft,
        firstFixRight: firstFixRight,
        lastFixRight: lastFixRight,
        firstFixLeft: firstFixLeft,
        isSticky: stickyOffsets.isSticky
    };
}
}}),
"[project]/node_modules/rc-table/es/Footer/SummaryContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var SummaryContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({});
const __TURBOPACK__default__export__ = SummaryContext;
}}),
"[project]/node_modules/rc-table/es/Footer/Cell.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SummaryCell)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Cell/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$fixUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/fixUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$SummaryContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Footer/SummaryContext.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
function SummaryCell(_ref) {
    var className = _ref.className, index = _ref.index, children = _ref.children, _ref$colSpan = _ref.colSpan, colSpan = _ref$colSpan === void 0 ? 1 : _ref$colSpan, rowSpan = _ref.rowSpan, align = _ref.align;
    var _useContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], [
        'prefixCls',
        'direction'
    ]), prefixCls = _useContext.prefixCls, direction = _useContext.direction;
    var _React$useContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$SummaryContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]), scrollColumnIndex = _React$useContext.scrollColumnIndex, stickyOffsets = _React$useContext.stickyOffsets, flattenColumns = _React$useContext.flattenColumns;
    var lastIndex = index + colSpan - 1;
    var mergedColSpan = lastIndex + 1 === scrollColumnIndex ? colSpan + 1 : colSpan;
    var fixedInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$fixUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCellFixedInfo"])(index, index + mergedColSpan - 1, flattenColumns, stickyOffsets, direction);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        className: className,
        index: index,
        component: "td",
        prefixCls: prefixCls,
        record: null,
        dataIndex: null,
        align: align,
        colSpan: mergedColSpan,
        rowSpan: rowSpan,
        render: function render() {
            return children;
        }
    }, fixedInfo));
}
}}),
"[project]/node_modules/rc-table/es/Footer/Row.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>FooterRow)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _excluded = [
    "children"
];
;
function FooterRow(_ref) {
    var children = _ref.children, props = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref, _excluded);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("tr", props, children);
}
}}),
"[project]/node_modules/rc-table/es/Footer/Summary.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$Cell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Footer/Cell.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$Row$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Footer/Row.js [app-client] (ecmascript)");
;
;
/**
 * Syntactic sugar. Do not support HOC.
 */ function Summary(_ref) {
    var children = _ref.children;
    return children;
}
Summary.Row = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$Row$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Summary.Cell = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$Cell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
const __TURBOPACK__default__export__ = Summary;
}}),
"[project]/node_modules/rc-table/es/Footer/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FooterComponents": (()=>FooterComponents),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useRenderTimes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$Summary$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Footer/Summary.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$SummaryContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Footer/SummaryContext.js [app-client] (ecmascript)");
;
;
;
;
;
;
function Footer(props) {
    if ("TURBOPACK compile-time truthy", 1) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props);
    }
    var children = props.children, stickyOffsets = props.stickyOffsets, flattenColumns = props.flattenColumns;
    var prefixCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], 'prefixCls');
    var lastColumnIndex = flattenColumns.length - 1;
    var scrollColumn = flattenColumns[lastColumnIndex];
    var summaryContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Footer.useMemo[summaryContext]": function() {
            return {
                stickyOffsets: stickyOffsets,
                flattenColumns: flattenColumns,
                scrollColumnIndex: scrollColumn !== null && scrollColumn !== void 0 && scrollColumn.scrollbar ? lastColumnIndex : null
            };
        }
    }["Footer.useMemo[summaryContext]"], [
        scrollColumn,
        flattenColumns,
        lastColumnIndex,
        stickyOffsets
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$SummaryContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Provider, {
        value: summaryContext
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("tfoot", {
        className: "".concat(prefixCls, "-summary")
    }, children));
}
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["responseImmutable"])(Footer);
var FooterComponents = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$Summary$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
}}),
"[project]/node_modules/rc-table/es/sugar/Column.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* istanbul ignore next */ /**
 * This is a syntactic sugar for `columns` prop.
 * So HOC will not work on this.
 */ // eslint-disable-next-line @typescript-eslint/no-unused-vars
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
function Column(_) {
    return null;
}
const __TURBOPACK__default__export__ = Column;
}}),
"[project]/node_modules/rc-table/es/sugar/ColumnGroup.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* istanbul ignore next */ /**
 * This is a syntactic sugar for `columns` prop.
 * So HOC will not work on this.
 */ // eslint-disable-next-line @typescript-eslint/no-unused-vars
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
function ColumnGroup(_) {
    return null;
}
const __TURBOPACK__default__export__ = ColumnGroup;
}}),
"[project]/node_modules/rc-table/es/hooks/useFlattenRecords.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useFlattenRecords)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
// recursion (flat tree structure)
function fillRecords(list, record, indent, childrenColumnName, expandedKeys, getRowKey, index) {
    var key = getRowKey(record, index);
    list.push({
        record: record,
        indent: indent,
        index: index,
        rowKey: key
    });
    var expanded = expandedKeys === null || expandedKeys === void 0 ? void 0 : expandedKeys.has(key);
    if (record && Array.isArray(record[childrenColumnName]) && expanded) {
        // expanded state, flat record
        for(var i = 0; i < record[childrenColumnName].length; i += 1){
            fillRecords(list, record[childrenColumnName][i], indent + 1, childrenColumnName, expandedKeys, getRowKey, i);
        }
    }
}
function useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey) {
    var arr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useFlattenRecords.useMemo[arr]": function() {
            if (expandedKeys !== null && expandedKeys !== void 0 && expandedKeys.size) {
                var list = [];
                // collect flattened record
                for(var i = 0; i < (data === null || data === void 0 ? void 0 : data.length); i += 1){
                    var record = data[i];
                    // using array.push or spread operator may cause "Maximum call stack size exceeded" exception if array size is big enough.
                    fillRecords(list, record, 0, childrenColumnName, expandedKeys, getRowKey, i);
                }
                return list;
            }
            return data === null || data === void 0 ? void 0 : data.map({
                "useFlattenRecords.useMemo[arr]": function(item, index) {
                    return {
                        record: item,
                        indent: 0,
                        index: index,
                        rowKey: getRowKey(item, index)
                    };
                }
            }["useFlattenRecords.useMemo[arr]"]);
        }
    }["useFlattenRecords.useMemo[arr]"], [
        data,
        childrenColumnName,
        expandedKeys,
        getRowKey
    ]);
    return arr;
}
}}),
"[project]/node_modules/rc-table/es/hooks/useRowInfo.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useRowInfo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/valueUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useEvent.js [app-client] (ecmascript) <export default as useEvent>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
;
;
;
;
;
;
function useRowInfo(record, rowKey, recordIndex, indent) {
    var context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], [
        'prefixCls',
        'fixedInfoList',
        'flattenColumns',
        'expandableType',
        'expandRowByClick',
        'onTriggerExpand',
        'rowClassName',
        'expandedRowClassName',
        'indentSize',
        'expandIcon',
        'expandedRowRender',
        'expandIconColumnIndex',
        'expandedKeys',
        'childrenColumnName',
        'rowExpandable',
        'onRow'
    ]);
    var flattenColumns = context.flattenColumns, expandableType = context.expandableType, expandedKeys = context.expandedKeys, childrenColumnName = context.childrenColumnName, onTriggerExpand = context.onTriggerExpand, rowExpandable = context.rowExpandable, onRow = context.onRow, expandRowByClick = context.expandRowByClick, rowClassName = context.rowClassName;
    // ======================= Expandable =======================
    // Only when row is not expandable and `children` exist in record
    var nestExpandable = expandableType === 'nest';
    var rowSupportExpand = expandableType === 'row' && (!rowExpandable || rowExpandable(record));
    var mergedExpandable = rowSupportExpand || nestExpandable;
    var expanded = expandedKeys && expandedKeys.has(rowKey);
    var hasNestChildren = childrenColumnName && record && record[childrenColumnName];
    var onInternalTriggerExpand = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__["useEvent"])(onTriggerExpand);
    // ========================= onRow ==========================
    var rowProps = onRow === null || onRow === void 0 ? void 0 : onRow(record, recordIndex);
    var onRowClick = rowProps === null || rowProps === void 0 ? void 0 : rowProps.onClick;
    var onClick = function onClick(event) {
        if (expandRowByClick && mergedExpandable) {
            onTriggerExpand(record, event);
        }
        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
            args[_key - 1] = arguments[_key];
        }
        onRowClick === null || onRowClick === void 0 || onRowClick.apply(void 0, [
            event
        ].concat(args));
    };
    // ====================== RowClassName ======================
    var computeRowClassName;
    if (typeof rowClassName === 'string') {
        computeRowClassName = rowClassName;
    } else if (typeof rowClassName === 'function') {
        computeRowClassName = rowClassName(record, recordIndex, indent);
    }
    // ========================= Column =========================
    var columnsKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getColumnsKey"])(flattenColumns);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, context), {}, {
        columnsKey: columnsKey,
        nestExpandable: nestExpandable,
        expanded: expanded,
        hasNestChildren: hasNestChildren,
        record: record,
        onTriggerExpand: onInternalTriggerExpand,
        rowSupportExpand: rowSupportExpand,
        expandable: mergedExpandable,
        rowProps: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, rowProps), {}, {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(computeRowClassName, rowProps === null || rowProps === void 0 ? void 0 : rowProps.className),
            onClick: onClick
        })
    });
}
}}),
"[project]/node_modules/rc-table/es/Body/ExpandedRow.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Cell/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useRenderTimes.js [app-client] (ecmascript)");
;
;
;
;
;
function ExpandedRow(props) {
    if ("TURBOPACK compile-time truthy", 1) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props);
    }
    var prefixCls = props.prefixCls, children = props.children, Component = props.component, cellComponent = props.cellComponent, className = props.className, expanded = props.expanded, colSpan = props.colSpan, isEmpty = props.isEmpty, _props$stickyOffset = props.stickyOffset, stickyOffset = _props$stickyOffset === void 0 ? 0 : _props$stickyOffset;
    var _useContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], [
        'scrollbarSize',
        'fixHeader',
        'fixColumn',
        'componentWidth',
        'horizonScroll'
    ]), scrollbarSize = _useContext.scrollbarSize, fixHeader = _useContext.fixHeader, fixColumn = _useContext.fixColumn, componentWidth = _useContext.componentWidth, horizonScroll = _useContext.horizonScroll;
    // Cache render node
    var contentNode = children;
    if (isEmpty ? horizonScroll && componentWidth : fixColumn) {
        contentNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
            style: {
                width: componentWidth - stickyOffset - (fixHeader && !isEmpty ? scrollbarSize : 0),
                position: 'sticky',
                left: stickyOffset,
                overflow: 'hidden'
            },
            className: "".concat(prefixCls, "-expanded-row-fixed")
        }, contentNode);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(Component, {
        className: className,
        style: {
            display: expanded ? null : 'none'
        }
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        component: cellComponent,
        prefixCls: prefixCls,
        colSpan: colSpan
    }, contentNode));
}
const __TURBOPACK__default__export__ = ExpandedRow;
}}),
"[project]/node_modules/rc-table/es/utils/expandUtil.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "computedExpandedClassName": (()=>computedExpandedClassName),
    "findAllChildrenKeys": (()=>findAllChildrenKeys),
    "renderExpandIcon": (()=>renderExpandIcon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/defineProperty.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
;
;
;
function renderExpandIcon(_ref) {
    var prefixCls = _ref.prefixCls, record = _ref.record, onExpand = _ref.onExpand, expanded = _ref.expanded, expandable = _ref.expandable;
    var expandClassName = "".concat(prefixCls, "-row-expand-icon");
    if (!expandable) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(expandClassName, "".concat(prefixCls, "-row-spaced"))
        });
    }
    var onClick = function onClick(event) {
        onExpand(record, event);
        event.stopPropagation();
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(expandClassName, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, "".concat(prefixCls, "-row-expanded"), expanded), "".concat(prefixCls, "-row-collapsed"), !expanded)),
        onClick: onClick
    });
}
function findAllChildrenKeys(data, getRowKey, childrenColumnName) {
    var keys = [];
    function dig(list) {
        (list || []).forEach(function(item, index) {
            keys.push(getRowKey(item, index));
            dig(item[childrenColumnName]);
        });
    }
    dig(data);
    return keys;
}
function computedExpandedClassName(cls, record, index, indent) {
    if (typeof cls === 'string') {
        return cls;
    }
    if (typeof cls === 'function') {
        return cls(record, index, indent);
    }
    return '';
}
}}),
"[project]/node_modules/rc-table/es/Body/BodyRow.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "getCellProps": (()=>getCellProps)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/defineProperty.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Cell/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useRenderTimes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRowInfo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useRowInfo.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$ExpandedRow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Body/ExpandedRow.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$expandUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/expandUtil.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
function getCellProps(rowInfo, column, colIndex, indent, index) {
    var _column$onCell;
    var rowKeys = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : [];
    var expandedRowOffset = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : 0;
    var record = rowInfo.record, prefixCls = rowInfo.prefixCls, columnsKey = rowInfo.columnsKey, fixedInfoList = rowInfo.fixedInfoList, expandIconColumnIndex = rowInfo.expandIconColumnIndex, nestExpandable = rowInfo.nestExpandable, indentSize = rowInfo.indentSize, expandIcon = rowInfo.expandIcon, expanded = rowInfo.expanded, hasNestChildren = rowInfo.hasNestChildren, onTriggerExpand = rowInfo.onTriggerExpand, expandable = rowInfo.expandable, expandedKeys = rowInfo.expandedKeys;
    var key = columnsKey[colIndex];
    var fixedInfo = fixedInfoList[colIndex];
    // ============= Used for nest expandable =============
    var appendCellNode;
    if (colIndex === (expandIconColumnIndex || 0) && nestExpandable) {
        appendCellNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
            style: {
                paddingLeft: "".concat(indentSize * indent, "px")
            },
            className: "".concat(prefixCls, "-row-indent indent-level-").concat(indent)
        }), expandIcon({
            prefixCls: prefixCls,
            expanded: expanded,
            expandable: hasNestChildren,
            record: record,
            onExpand: onTriggerExpand
        }));
    }
    var additionalCellProps = ((_column$onCell = column.onCell) === null || _column$onCell === void 0 ? void 0 : _column$onCell.call(column, record, index)) || {};
    // Expandable row has offset
    if (expandedRowOffset) {
        var _additionalCellProps$ = additionalCellProps.rowSpan, rowSpan = _additionalCellProps$ === void 0 ? 1 : _additionalCellProps$;
        // For expandable row with rowSpan,
        // We should increase the rowSpan if the row is expanded
        if (expandable && rowSpan && colIndex < expandedRowOffset) {
            var currentRowSpan = rowSpan;
            for(var i = index; i < index + rowSpan; i += 1){
                var rowKey = rowKeys[i];
                if (expandedKeys.has(rowKey)) {
                    currentRowSpan += 1;
                }
            }
            additionalCellProps.rowSpan = currentRowSpan;
        }
    }
    return {
        key: key,
        fixedInfo: fixedInfo,
        appendCellNode: appendCellNode,
        additionalCellProps: additionalCellProps
    };
}
// ==================================================================================
// ==                                 getCellProps                                 ==
// ==================================================================================
function BodyRow(props) {
    if ("TURBOPACK compile-time truthy", 1) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props);
    }
    var className = props.className, style = props.style, record = props.record, index = props.index, renderIndex = props.renderIndex, rowKey = props.rowKey, rowKeys = props.rowKeys, _props$indent = props.indent, indent = _props$indent === void 0 ? 0 : _props$indent, RowComponent = props.rowComponent, cellComponent = props.cellComponent, scopeCellComponent = props.scopeCellComponent, expandedRowInfo = props.expandedRowInfo;
    var rowInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRowInfo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(record, rowKey, index, indent);
    var prefixCls = rowInfo.prefixCls, flattenColumns = rowInfo.flattenColumns, expandedRowClassName = rowInfo.expandedRowClassName, expandedRowRender = rowInfo.expandedRowRender, rowProps = rowInfo.rowProps, expanded = rowInfo.expanded, rowSupportExpand = rowInfo.rowSupportExpand;
    // Force render expand row if expanded before
    var expandedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    expandedRef.current || (expandedRef.current = expanded);
    if ("TURBOPACK compile-time truthy", 1) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props);
    }
    // 若没有 expandedRowRender 参数, 将使用 baseRowNode 渲染 Children
    // 此时如果 level > 1 则说明是 expandedRow, 一样需要附加 computedExpandedRowClassName
    var expandedClsName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$expandUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["computedExpandedClassName"])(expandedRowClassName, record, index, indent);
    // ======================== Base tr row ========================
    var baseRowNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(RowComponent, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, rowProps, {
        "data-row-key": rowKey,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(className, "".concat(prefixCls, "-row"), "".concat(prefixCls, "-row-level-").concat(indent), rowProps === null || rowProps === void 0 ? void 0 : rowProps.className, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, expandedClsName, indent >= 1)),
        style: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, style), rowProps === null || rowProps === void 0 ? void 0 : rowProps.style)
    }), flattenColumns.map(function(column, colIndex) {
        var render = column.render, dataIndex = column.dataIndex, columnClassName = column.className;
        var _getCellProps = getCellProps(rowInfo, column, colIndex, indent, index, rowKeys, expandedRowInfo === null || expandedRowInfo === void 0 ? void 0 : expandedRowInfo.offset), key = _getCellProps.key, fixedInfo = _getCellProps.fixedInfo, appendCellNode = _getCellProps.appendCellNode, additionalCellProps = _getCellProps.additionalCellProps;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
            className: columnClassName,
            ellipsis: column.ellipsis,
            align: column.align,
            scope: column.rowScope,
            component: column.rowScope ? scopeCellComponent : cellComponent,
            prefixCls: prefixCls,
            key: key,
            record: record,
            index: index,
            renderIndex: renderIndex,
            dataIndex: dataIndex,
            render: render,
            shouldCellUpdate: column.shouldCellUpdate
        }, fixedInfo, {
            appendNode: appendCellNode,
            additionalProps: additionalCellProps
        }));
    }));
    // ======================== Expand Row =========================
    var expandRowNode;
    if (rowSupportExpand && (expandedRef.current || expanded)) {
        var expandContent = expandedRowRender(record, index, indent + 1, expanded);
        expandRowNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$ExpandedRow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            expanded: expanded,
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("".concat(prefixCls, "-expanded-row"), "".concat(prefixCls, "-expanded-row-level-").concat(indent + 1), expandedClsName),
            prefixCls: prefixCls,
            component: RowComponent,
            cellComponent: cellComponent,
            colSpan: expandedRowInfo ? expandedRowInfo.colSpan : flattenColumns.length,
            stickyOffset: expandedRowInfo === null || expandedRowInfo === void 0 ? void 0 : expandedRowInfo.sticky,
            isEmpty: false
        }, expandContent);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, baseRowNode, expandRowNode);
}
if ("TURBOPACK compile-time truthy", 1) {
    BodyRow.displayName = 'BodyRow';
}
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["responseImmutable"])(BodyRow);
}}),
"[project]/node_modules/rc-table/es/Body/MeasureCell.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>MeasureCell)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-resize-observer/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-resize-observer/es/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useLayoutEffect.js [app-client] (ecmascript)");
;
;
;
function MeasureCell(_ref) {
    var columnKey = _ref.columnKey, onColumnResize = _ref.onColumnResize;
    var cellRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "MeasureCell.useLayoutEffect": function() {
            if (cellRef.current) {
                onColumnResize(columnKey, cellRef.current.offsetWidth);
            }
        }
    }["MeasureCell.useLayoutEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], {
        data: columnKey
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("td", {
        ref: cellRef,
        style: {
            padding: 0,
            border: 0,
            height: 0
        }
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        style: {
            height: 0,
            overflow: 'hidden'
        }
    }, "\xA0")));
}
}}),
"[project]/node_modules/rc-table/es/Body/MeasureRow.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>MeasureRow)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-resize-observer/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-resize-observer/es/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$MeasureCell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Body/MeasureCell.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$isVisible$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/isVisible.js [app-client] (ecmascript)");
;
;
;
;
function MeasureRow(_ref) {
    var prefixCls = _ref.prefixCls, columnsKey = _ref.columnsKey, onColumnResize = _ref.onColumnResize;
    var ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("tr", {
        "aria-hidden": "true",
        className: "".concat(prefixCls, "-measure-row"),
        style: {
            height: 0,
            fontSize: 0
        },
        ref: ref
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].Collection, {
        onBatchResize: function onBatchResize(infoList) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$isVisible$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(ref.current)) {
                infoList.forEach(function(_ref2) {
                    var columnKey = _ref2.data, size = _ref2.size;
                    onColumnResize(columnKey, size.offsetWidth);
                });
            }
        }
    }, columnsKey.map(function(columnKey) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$MeasureCell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            key: columnKey,
            columnKey: columnKey,
            onColumnResize: onColumnResize
        });
    })));
}
}}),
"[project]/node_modules/rc-table/es/Body/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$PerfContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/PerfContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useFlattenRecords$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useFlattenRecords.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useRenderTimes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/valueUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$BodyRow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Body/BodyRow.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$ExpandedRow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Body/ExpandedRow.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$MeasureRow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Body/MeasureRow.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
function Body(props) {
    if ("TURBOPACK compile-time truthy", 1) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props);
    }
    var data = props.data, measureColumnWidth = props.measureColumnWidth;
    var _useContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], [
        'prefixCls',
        'getComponent',
        'onColumnResize',
        'flattenColumns',
        'getRowKey',
        'expandedKeys',
        'childrenColumnName',
        'emptyNode',
        'expandedRowOffset',
        'fixedInfoList',
        'colWidths'
    ]), prefixCls = _useContext.prefixCls, getComponent = _useContext.getComponent, onColumnResize = _useContext.onColumnResize, flattenColumns = _useContext.flattenColumns, getRowKey = _useContext.getRowKey, expandedKeys = _useContext.expandedKeys, childrenColumnName = _useContext.childrenColumnName, emptyNode = _useContext.emptyNode, _useContext$expandedR = _useContext.expandedRowOffset, expandedRowOffset = _useContext$expandedR === void 0 ? 0 : _useContext$expandedR, colWidths = _useContext.colWidths;
    var flattenData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useFlattenRecords$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(data, childrenColumnName, expandedKeys, getRowKey);
    var rowKeys = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Body.useMemo[rowKeys]": function() {
            return flattenData.map({
                "Body.useMemo[rowKeys]": function(item) {
                    return item.rowKey;
                }
            }["Body.useMemo[rowKeys]"]);
        }
    }["Body.useMemo[rowKeys]"], [
        flattenData
    ]);
    // =================== Performance ====================
    var perfRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({
        renderWithProps: false
    });
    // ===================== Expanded =====================
    // `expandedRowOffset` data is same for all the rows.
    // Let's calc on Body side to save performance.
    var expandedRowInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Body.useMemo[expandedRowInfo]": function() {
            var expandedColSpan = flattenColumns.length - expandedRowOffset;
            var expandedStickyStart = 0;
            for(var i = 0; i < expandedRowOffset; i += 1){
                expandedStickyStart += colWidths[i] || 0;
            }
            return {
                offset: expandedRowOffset,
                colSpan: expandedColSpan,
                sticky: expandedStickyStart
            };
        }
    }["Body.useMemo[expandedRowInfo]"], [
        flattenColumns.length,
        expandedRowOffset,
        colWidths
    ]);
    // ====================== Render ======================
    var WrapperComponent = getComponent([
        'body',
        'wrapper'
    ], 'tbody');
    var trComponent = getComponent([
        'body',
        'row'
    ], 'tr');
    var tdComponent = getComponent([
        'body',
        'cell'
    ], 'td');
    var thComponent = getComponent([
        'body',
        'cell'
    ], 'th');
    var rows;
    if (data.length) {
        rows = flattenData.map(function(item, idx) {
            var record = item.record, indent = item.indent, renderIndex = item.index, rowKey = item.rowKey;
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$BodyRow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                key: rowKey,
                rowKey: rowKey,
                rowKeys: rowKeys,
                record: record,
                index: idx,
                renderIndex: renderIndex,
                rowComponent: trComponent,
                cellComponent: tdComponent,
                scopeCellComponent: thComponent,
                indent: indent,
                expandedRowInfo: expandedRowInfo
            });
        });
    } else {
        rows = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$ExpandedRow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            expanded: true,
            className: "".concat(prefixCls, "-placeholder"),
            prefixCls: prefixCls,
            component: trComponent,
            cellComponent: tdComponent,
            colSpan: flattenColumns.length,
            isEmpty: true
        }, emptyNode);
    }
    var columnsKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getColumnsKey"])(flattenColumns);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$PerfContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Provider, {
        value: perfRef.current
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(WrapperComponent, {
        className: "".concat(prefixCls, "-tbody")
    }, measureColumnWidth && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$MeasureRow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        prefixCls: prefixCls,
        columnsKey: columnsKey,
        onColumnResize: onColumnResize
    }), rows));
}
if ("TURBOPACK compile-time truthy", 1) {
    Body.displayName = 'Body';
}
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["responseImmutable"])(Body);
}}),
"[project]/node_modules/rc-table/es/utils/legacyUtil.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "INTERNAL_COL_DEFINE": (()=>INTERNAL_COL_DEFINE),
    "getExpandableProps": (()=>getExpandableProps)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/warning.js [app-client] (ecmascript)");
;
;
var _excluded = [
    "expandable"
];
;
var INTERNAL_COL_DEFINE = 'RC_TABLE_INTERNAL_COL_DEFINE';
function getExpandableProps(props) {
    var expandable = props.expandable, legacyExpandableConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, _excluded);
    var config;
    if ('expandable' in props) {
        config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, legacyExpandableConfig), expandable);
    } else {
        if (("TURBOPACK compile-time value", "development") !== 'production' && [
            'indentSize',
            'expandedRowKeys',
            'defaultExpandedRowKeys',
            'defaultExpandAllRows',
            'expandedRowRender',
            'expandRowByClick',
            'expandIcon',
            'onExpand',
            'onExpandedRowsChange',
            'expandedRowClassName',
            'expandIconColumnIndex',
            'showExpandColumn',
            'title'
        ].some(function(prop) {
            return prop in props;
        })) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, 'expanded related props have been moved into `expandable`.');
        }
        config = legacyExpandableConfig;
    }
    if (config.showExpandColumn === false) {
        config.expandIconColumnIndex = -1;
    }
    return config;
}
}}),
"[project]/node_modules/rc-table/es/ColGroup.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/legacyUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
;
;
var _excluded = [
    "columnType"
];
;
;
;
;
function ColGroup(_ref) {
    var colWidths = _ref.colWidths, columns = _ref.columns, columCount = _ref.columCount;
    var _useContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], [
        'tableLayout'
    ]), tableLayout = _useContext.tableLayout;
    var cols = [];
    var len = columCount || columns.length;
    // Only insert col with width & additional props
    // Skip if rest col do not have any useful info
    var mustInsert = false;
    for(var i = len - 1; i >= 0; i -= 1){
        var width = colWidths[i];
        var column = columns && columns[i];
        var additionalProps = void 0;
        var minWidth = void 0;
        if (column) {
            additionalProps = column[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INTERNAL_COL_DEFINE"]];
            // fixed will cause layout problems
            if (tableLayout === 'auto') {
                minWidth = column.minWidth;
            }
        }
        if (width || minWidth || additionalProps || mustInsert) {
            var _ref2 = additionalProps || {}, columnType = _ref2.columnType, restAdditionalProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref2, _excluded);
            cols.unshift(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("col", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                key: i,
                style: {
                    width: width,
                    minWidth: minWidth
                }
            }, restAdditionalProps)));
            mustInsert = true;
        }
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("colgroup", null, cols);
}
const __TURBOPACK__default__export__ = ColGroup;
}}),
"[project]/node_modules/rc-table/es/FixedHolder/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/defineProperty.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/ref.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$ColGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/ColGroup.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useRenderTimes.js [app-client] (ecmascript)");
;
;
;
;
var _excluded = [
    "className",
    "noData",
    "columns",
    "flattenColumns",
    "colWidths",
    "columCount",
    "stickyOffsets",
    "direction",
    "fixHeader",
    "stickyTopOffset",
    "stickyBottomOffset",
    "stickyClassName",
    "onScroll",
    "maxContentScroll",
    "children"
];
;
;
;
;
;
;
;
;
function useColumnWidth(colWidths, columCount) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useColumnWidth.useMemo": function() {
            var cloneColumns = [];
            for(var i = 0; i < columCount; i += 1){
                var val = colWidths[i];
                if (val !== undefined) {
                    cloneColumns[i] = val;
                } else {
                    return null;
                }
            }
            return cloneColumns;
        }
    }["useColumnWidth.useMemo"], [
        colWidths.join('_'),
        columCount
    ]);
}
var FixedHolder = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(function(props, ref) {
    if ("TURBOPACK compile-time truthy", 1) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props);
    }
    var className = props.className, noData = props.noData, columns = props.columns, flattenColumns = props.flattenColumns, colWidths = props.colWidths, columCount = props.columCount, stickyOffsets = props.stickyOffsets, direction = props.direction, fixHeader = props.fixHeader, stickyTopOffset = props.stickyTopOffset, stickyBottomOffset = props.stickyBottomOffset, stickyClassName = props.stickyClassName, onScroll = props.onScroll, maxContentScroll = props.maxContentScroll, children = props.children, restProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, _excluded);
    var _useContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], [
        'prefixCls',
        'scrollbarSize',
        'isSticky',
        'getComponent'
    ]), prefixCls = _useContext.prefixCls, scrollbarSize = _useContext.scrollbarSize, isSticky = _useContext.isSticky, getComponent = _useContext.getComponent;
    var TableComponent = getComponent([
        'header',
        'table'
    ], 'table');
    var combinationScrollBarSize = isSticky && !fixHeader ? 0 : scrollbarSize;
    // Pass wheel to scroll event
    var scrollRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    var setScrollRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FixedHolder.useCallback[setScrollRef]": function(element) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fillRef"])(ref, element);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fillRef"])(scrollRef, element);
        }
    }["FixedHolder.useCallback[setScrollRef]"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FixedHolder.useEffect": function() {
            function onWheel(e) {
                var _ref = e, currentTarget = _ref.currentTarget, deltaX = _ref.deltaX;
                if (deltaX) {
                    onScroll({
                        currentTarget: currentTarget,
                        scrollLeft: currentTarget.scrollLeft + deltaX
                    });
                    e.preventDefault();
                }
            }
            var scrollEle = scrollRef.current;
            scrollEle === null || scrollEle === void 0 || scrollEle.addEventListener('wheel', onWheel, {
                passive: false
            });
            return ({
                "FixedHolder.useEffect": function() {
                    scrollEle === null || scrollEle === void 0 || scrollEle.removeEventListener('wheel', onWheel);
                }
            })["FixedHolder.useEffect"];
        }
    }["FixedHolder.useEffect"], []);
    // Check if all flattenColumns has width
    var allFlattenColumnsWithWidth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FixedHolder.useMemo[allFlattenColumnsWithWidth]": function() {
            return flattenColumns.every({
                "FixedHolder.useMemo[allFlattenColumnsWithWidth]": function(column) {
                    return column.width;
                }
            }["FixedHolder.useMemo[allFlattenColumnsWithWidth]"]);
        }
    }["FixedHolder.useMemo[allFlattenColumnsWithWidth]"], [
        flattenColumns
    ]);
    // Add scrollbar column
    var lastColumn = flattenColumns[flattenColumns.length - 1];
    var ScrollBarColumn = {
        fixed: lastColumn ? lastColumn.fixed : null,
        scrollbar: true,
        onHeaderCell: function onHeaderCell() {
            return {
                className: "".concat(prefixCls, "-cell-scrollbar")
            };
        }
    };
    var columnsWithScrollbar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FixedHolder.useMemo[columnsWithScrollbar]": function() {
            return combinationScrollBarSize ? [].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(columns), [
                ScrollBarColumn
            ]) : columns;
        }
    }["FixedHolder.useMemo[columnsWithScrollbar]"], [
        combinationScrollBarSize,
        columns
    ]);
    var flattenColumnsWithScrollbar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FixedHolder.useMemo[flattenColumnsWithScrollbar]": function() {
            return combinationScrollBarSize ? [].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(flattenColumns), [
                ScrollBarColumn
            ]) : flattenColumns;
        }
    }["FixedHolder.useMemo[flattenColumnsWithScrollbar]"], [
        combinationScrollBarSize,
        flattenColumns
    ]);
    // Calculate the sticky offsets
    var headerStickyOffsets = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FixedHolder.useMemo[headerStickyOffsets]": function() {
            var right = stickyOffsets.right, left = stickyOffsets.left;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, stickyOffsets), {}, {
                left: direction === 'rtl' ? [].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(left.map({
                    "FixedHolder.useMemo[headerStickyOffsets]": function(width) {
                        return width + combinationScrollBarSize;
                    }
                }["FixedHolder.useMemo[headerStickyOffsets]"])), [
                    0
                ]) : left,
                right: direction === 'rtl' ? right : [].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(right.map({
                    "FixedHolder.useMemo[headerStickyOffsets]": function(width) {
                        return width + combinationScrollBarSize;
                    }
                }["FixedHolder.useMemo[headerStickyOffsets]"])), [
                    0
                ]),
                isSticky: isSticky
            });
        }
    }["FixedHolder.useMemo[headerStickyOffsets]"], [
        combinationScrollBarSize,
        stickyOffsets,
        isSticky
    ]);
    var mergedColumnWidth = useColumnWidth(colWidths, columCount);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        style: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
            overflow: 'hidden'
        }, isSticky ? {
            top: stickyTopOffset,
            bottom: stickyBottomOffset
        } : {}),
        ref: setScrollRef,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(className, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, stickyClassName, !!stickyClassName))
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(TableComponent, {
        style: {
            tableLayout: 'fixed',
            visibility: noData || mergedColumnWidth ? null : 'hidden'
        }
    }, (!noData || !maxContentScroll || allFlattenColumnsWithWidth) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$ColGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        colWidths: mergedColumnWidth ? [].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(mergedColumnWidth), [
            combinationScrollBarSize
        ]) : [],
        columCount: columCount + 1,
        columns: flattenColumnsWithScrollbar
    }), children((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, restProps), {}, {
        stickyOffsets: headerStickyOffsets,
        columns: columnsWithScrollbar,
        flattenColumns: flattenColumnsWithScrollbar
    }))));
});
if ("TURBOPACK compile-time truthy", 1) {
    FixedHolder.displayName = 'FixedHolder';
}
const __TURBOPACK__default__export__ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(FixedHolder);
}}),
"[project]/node_modules/rc-table/es/Header/HeaderRow.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Cell/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$fixUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/fixUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/valueUtil.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
var HeaderRow = function HeaderRow(props) {
    var cells = props.cells, stickyOffsets = props.stickyOffsets, flattenColumns = props.flattenColumns, RowComponent = props.rowComponent, CellComponent = props.cellComponent, onHeaderRow = props.onHeaderRow, index = props.index;
    var _useContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], [
        'prefixCls',
        'direction'
    ]), prefixCls = _useContext.prefixCls, direction = _useContext.direction;
    var rowProps;
    if (onHeaderRow) {
        rowProps = onHeaderRow(cells.map(function(cell) {
            return cell.column;
        }), index);
    }
    var columnsKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getColumnsKey"])(cells.map(function(cell) {
        return cell.column;
    }));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(RowComponent, rowProps, cells.map(function(cell, cellIndex) {
        var column = cell.column;
        var fixedInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$fixUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCellFixedInfo"])(cell.colStart, cell.colEnd, flattenColumns, stickyOffsets, direction);
        var additionalProps;
        if (column && column.onHeaderCell) {
            additionalProps = cell.column.onHeaderCell(column);
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, cell, {
            scope: column.title ? cell.colSpan > 1 ? 'colgroup' : 'col' : null,
            ellipsis: column.ellipsis,
            align: column.align,
            component: CellComponent,
            prefixCls: prefixCls,
            key: columnsKey[cellIndex]
        }, fixedInfo, {
            additionalProps: additionalProps,
            rowType: "header"
        }));
    }));
};
if ("TURBOPACK compile-time truthy", 1) {
    HeaderRow.displayName = 'HeaderRow';
}
const __TURBOPACK__default__export__ = HeaderRow;
}}),
"[project]/node_modules/rc-table/es/Header/Header.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useRenderTimes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Header$2f$HeaderRow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Header/HeaderRow.js [app-client] (ecmascript)");
;
;
;
;
;
function parseHeaderRows(rootColumns) {
    var rows = [];
    function fillRowCells(columns, colIndex) {
        var rowIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
        // Init rows
        rows[rowIndex] = rows[rowIndex] || [];
        var currentColIndex = colIndex;
        var colSpans = columns.filter(Boolean).map(function(column) {
            var cell = {
                key: column.key,
                className: column.className || '',
                children: column.title,
                column: column,
                colStart: currentColIndex
            };
            var colSpan = 1;
            var subColumns = column.children;
            if (subColumns && subColumns.length > 0) {
                colSpan = fillRowCells(subColumns, currentColIndex, rowIndex + 1).reduce(function(total, count) {
                    return total + count;
                }, 0);
                cell.hasSubColumns = true;
            }
            if ('colSpan' in column) {
                colSpan = column.colSpan;
            }
            if ('rowSpan' in column) {
                cell.rowSpan = column.rowSpan;
            }
            cell.colSpan = colSpan;
            cell.colEnd = cell.colStart + colSpan - 1;
            rows[rowIndex].push(cell);
            currentColIndex += colSpan;
            return colSpan;
        });
        return colSpans;
    }
    // Generate `rows` cell data
    fillRowCells(rootColumns, 0);
    // Handle `rowSpan`
    var rowCount = rows.length;
    var _loop = function _loop(rowIndex) {
        rows[rowIndex].forEach(function(cell) {
            if (!('rowSpan' in cell) && !cell.hasSubColumns) {
                // eslint-disable-next-line no-param-reassign
                cell.rowSpan = rowCount - rowIndex;
            }
        });
    };
    for(var rowIndex = 0; rowIndex < rowCount; rowIndex += 1){
        _loop(rowIndex);
    }
    return rows;
}
var Header = function Header(props) {
    if ("TURBOPACK compile-time truthy", 1) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRenderTimes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props);
    }
    var stickyOffsets = props.stickyOffsets, columns = props.columns, flattenColumns = props.flattenColumns, onHeaderRow = props.onHeaderRow;
    var _useContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], [
        'prefixCls',
        'getComponent'
    ]), prefixCls = _useContext.prefixCls, getComponent = _useContext.getComponent;
    var rows = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Header.useMemo[rows]": function() {
            return parseHeaderRows(columns);
        }
    }["Header.useMemo[rows]"], [
        columns
    ]);
    var WrapperComponent = getComponent([
        'header',
        'wrapper'
    ], 'thead');
    var trComponent = getComponent([
        'header',
        'row'
    ], 'tr');
    var thComponent = getComponent([
        'header',
        'cell'
    ], 'th');
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(WrapperComponent, {
        className: "".concat(prefixCls, "-thead")
    }, rows.map(function(row, rowIndex) {
        var rowNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Header$2f$HeaderRow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            key: rowIndex,
            flattenColumns: flattenColumns,
            cells: row,
            stickyOffsets: stickyOffsets,
            rowComponent: trComponent,
            cellComponent: thComponent,
            onHeaderRow: onHeaderRow,
            index: rowIndex
        });
        return rowNode;
    }));
};
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["responseImmutable"])(Header);
}}),
"[project]/node_modules/rc-table/es/hooks/useColumns/useWidthColumns.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useWidthColumns)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
function parseColWidth(totalWidth) {
    var width = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
    if (typeof width === 'number') {
        return width;
    }
    if (width.endsWith('%')) {
        return totalWidth * parseFloat(width) / 100;
    }
    return null;
}
function useWidthColumns(flattenColumns, scrollWidth, clientWidth) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useWidthColumns.useMemo": function() {
            // Fill width if needed
            if (scrollWidth && scrollWidth > 0) {
                var totalWidth = 0;
                var missWidthCount = 0;
                // collect not given width column
                flattenColumns.forEach({
                    "useWidthColumns.useMemo": function(col) {
                        var colWidth = parseColWidth(scrollWidth, col.width);
                        if (colWidth) {
                            totalWidth += colWidth;
                        } else {
                            missWidthCount += 1;
                        }
                    }
                }["useWidthColumns.useMemo"]);
                // Fill width
                var maxFitWidth = Math.max(scrollWidth, clientWidth);
                var restWidth = Math.max(maxFitWidth - totalWidth, missWidthCount);
                var restCount = missWidthCount;
                var avgWidth = restWidth / missWidthCount;
                var realTotal = 0;
                var filledColumns = flattenColumns.map({
                    "useWidthColumns.useMemo.filledColumns": function(col) {
                        var clone = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, col);
                        var colWidth = parseColWidth(scrollWidth, clone.width);
                        if (colWidth) {
                            clone.width = colWidth;
                        } else {
                            var colAvgWidth = Math.floor(avgWidth);
                            clone.width = restCount === 1 ? restWidth : colAvgWidth;
                            restWidth -= colAvgWidth;
                            restCount -= 1;
                        }
                        realTotal += clone.width;
                        return clone;
                    }
                }["useWidthColumns.useMemo.filledColumns"]);
                // If realTotal is less than clientWidth,
                // We need extend column width
                if (realTotal < maxFitWidth) {
                    var scale = maxFitWidth / realTotal;
                    restWidth = maxFitWidth;
                    filledColumns.forEach({
                        "useWidthColumns.useMemo": function(col, index) {
                            var colWidth = Math.floor(col.width * scale);
                            col.width = index === filledColumns.length - 1 ? restWidth : colWidth;
                            restWidth -= colWidth;
                        }
                    }["useWidthColumns.useMemo"]);
                }
                return [
                    filledColumns,
                    Math.max(realTotal, maxFitWidth)
                ];
            }
            return [
                flattenColumns,
                scrollWidth
            ];
        }
    }["useWidthColumns.useMemo"], [
        flattenColumns,
        scrollWidth,
        clientWidth
    ]);
}
}}),
"[project]/node_modules/rc-table/es/hooks/useColumns/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "convertChildrenToColumns": (()=>convertChildrenToColumns),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/defineProperty.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Children$2f$toArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Children/toArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/constant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/legacyUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useColumns$2f$useWidthColumns$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useColumns/useWidthColumns.js [app-client] (ecmascript)");
;
;
;
;
;
;
var _excluded = [
    "children"
], _excluded2 = [
    "fixed"
];
;
;
;
;
;
;
function convertChildrenToColumns(children) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Children$2f$toArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(children).filter(function(node) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(node);
    }).map(function(_ref) {
        var key = _ref.key, props = _ref.props;
        var nodeChildren = props.children, restProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, _excluded);
        var column = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
            key: key
        }, restProps);
        if (nodeChildren) {
            column.children = convertChildrenToColumns(nodeChildren);
        }
        return column;
    });
}
function filterHiddenColumns(columns) {
    return columns.filter(function(column) {
        return column && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(column) === 'object' && !column.hidden;
    }).map(function(column) {
        var subColumns = column.children;
        if (subColumns && subColumns.length > 0) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, column), {}, {
                children: filterHiddenColumns(subColumns)
            });
        }
        return column;
    });
}
function flatColumns(columns) {
    var parentKey = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'key';
    return columns.filter(function(column) {
        return column && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(column) === 'object';
    }).reduce(function(list, column, index) {
        var fixed = column.fixed;
        // Convert `fixed='true'` to `fixed='left'` instead
        var parsedFixed = fixed === true ? 'left' : fixed;
        var mergedKey = "".concat(parentKey, "-").concat(index);
        var subColumns = column.children;
        if (subColumns && subColumns.length > 0) {
            return [].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(list), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(flatColumns(subColumns, mergedKey).map(function(subColum) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                    fixed: parsedFixed
                }, subColum);
            })));
        }
        return [].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(list), [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                key: mergedKey
            }, column), {}, {
                fixed: parsedFixed
            })
        ]);
    }, []);
}
function revertForRtl(columns) {
    return columns.map(function(column) {
        var fixed = column.fixed, restProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(column, _excluded2);
        // Convert `fixed='left'` to `fixed='right'` instead
        var parsedFixed = fixed;
        if (fixed === 'left') {
            parsedFixed = 'right';
        } else if (fixed === 'right') {
            parsedFixed = 'left';
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
            fixed: parsedFixed
        }, restProps);
    });
}
/**
 * Parse `columns` & `children` into `columns`.
 */ function useColumns(_ref2, transformColumns) {
    var prefixCls = _ref2.prefixCls, columns = _ref2.columns, children = _ref2.children, expandable = _ref2.expandable, expandedKeys = _ref2.expandedKeys, columnTitle = _ref2.columnTitle, getRowKey = _ref2.getRowKey, onTriggerExpand = _ref2.onTriggerExpand, expandIcon = _ref2.expandIcon, rowExpandable = _ref2.rowExpandable, expandIconColumnIndex = _ref2.expandIconColumnIndex, _ref2$expandedRowOffs = _ref2.expandedRowOffset, expandedRowOffset = _ref2$expandedRowOffs === void 0 ? 0 : _ref2$expandedRowOffs, direction = _ref2.direction, expandRowByClick = _ref2.expandRowByClick, columnWidth = _ref2.columnWidth, fixed = _ref2.fixed, scrollWidth = _ref2.scrollWidth, clientWidth = _ref2.clientWidth;
    var baseColumns = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useColumns.useMemo[baseColumns]": function() {
            var newColumns = columns || convertChildrenToColumns(children) || [];
            return filterHiddenColumns(newColumns.slice());
        }
    }["useColumns.useMemo[baseColumns]"], [
        columns,
        children
    ]);
    // ========================== Expand ==========================
    var withExpandColumns = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useColumns.useMemo[withExpandColumns]": function() {
            if (expandable) {
                var cloneColumns = baseColumns.slice();
                // >>> Warning if use `expandIconColumnIndex`
                if (("TURBOPACK compile-time value", "development") !== 'production' && expandIconColumnIndex >= 0) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, '`expandIconColumnIndex` is deprecated. Please use `Table.EXPAND_COLUMN` in `columns` instead.');
                }
                // >>> Insert expand column if not exist
                if (!cloneColumns.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EXPAND_COLUMN"])) {
                    var expandColIndex = expandIconColumnIndex || 0;
                    if (expandColIndex >= 0 && (expandColIndex || fixed === 'left' || !fixed)) {
                        cloneColumns.splice(expandColIndex, 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EXPAND_COLUMN"]);
                    }
                    if (fixed === 'right') {
                        cloneColumns.splice(baseColumns.length, 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EXPAND_COLUMN"]);
                    }
                }
                // >>> Deduplicate additional expand column
                if (("TURBOPACK compile-time value", "development") !== 'production' && cloneColumns.filter({
                    "useColumns.useMemo[withExpandColumns]": function(c) {
                        return c === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EXPAND_COLUMN"];
                    }
                }["useColumns.useMemo[withExpandColumns]"]).length > 1) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, 'There exist more than one `EXPAND_COLUMN` in `columns`.');
                }
                var expandColumnIndex = cloneColumns.indexOf(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EXPAND_COLUMN"]);
                cloneColumns = cloneColumns.filter({
                    "useColumns.useMemo[withExpandColumns]": function(column, index) {
                        return column !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EXPAND_COLUMN"] || index === expandColumnIndex;
                    }
                }["useColumns.useMemo[withExpandColumns]"]);
                // >>> Check if expand column need to fixed
                var prevColumn = baseColumns[expandColumnIndex];
                var fixedColumn;
                if (fixed) {
                    fixedColumn = fixed;
                } else {
                    fixedColumn = prevColumn ? prevColumn.fixed : null;
                }
                // >>> Create expandable column
                var expandColumn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INTERNAL_COL_DEFINE"], {
                    className: "".concat(prefixCls, "-expand-icon-col"),
                    columnType: 'EXPAND_COLUMN'
                }), "title", columnTitle), "fixed", fixedColumn), "className", "".concat(prefixCls, "-row-expand-icon-cell")), "width", columnWidth), "render", function render(_, record, index) {
                    var rowKey = getRowKey(record, index);
                    var expanded = expandedKeys.has(rowKey);
                    var recordExpandable = rowExpandable ? rowExpandable(record) : true;
                    var icon = expandIcon({
                        prefixCls: prefixCls,
                        expanded: expanded,
                        expandable: recordExpandable,
                        record: record,
                        onExpand: onTriggerExpand
                    });
                    if (expandRowByClick) {
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
                            onClick: function onClick(e) {
                                return e.stopPropagation();
                            }
                        }, icon);
                    }
                    return icon;
                });
                return cloneColumns.map({
                    "useColumns.useMemo[withExpandColumns]": function(col, index) {
                        var column = col === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EXPAND_COLUMN"] ? expandColumn : col;
                        if (index < expandedRowOffset) {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, column), {}, {
                                fixed: column.fixed || 'left'
                            });
                        }
                        return column;
                    }
                }["useColumns.useMemo[withExpandColumns]"]);
            }
            if (("TURBOPACK compile-time value", "development") !== 'production' && baseColumns.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EXPAND_COLUMN"])) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, '`expandable` is not config but there exist `EXPAND_COLUMN` in `columns`.');
            }
            return baseColumns.filter({
                "useColumns.useMemo[withExpandColumns]": function(col) {
                    return col !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EXPAND_COLUMN"];
                }
            }["useColumns.useMemo[withExpandColumns]"]);
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["useColumns.useMemo[withExpandColumns]"], [
        expandable,
        baseColumns,
        getRowKey,
        expandedKeys,
        expandIcon,
        direction,
        expandedRowOffset
    ]);
    // ========================= Transform ========================
    var mergedColumns = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useColumns.useMemo[mergedColumns]": function() {
            var finalColumns = withExpandColumns;
            if (transformColumns) {
                finalColumns = transformColumns(finalColumns);
            }
            // Always provides at least one column for table display
            if (!finalColumns.length) {
                finalColumns = [
                    {
                        render: function render() {
                            return null;
                        }
                    }
                ];
            }
            return finalColumns;
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["useColumns.useMemo[mergedColumns]"], [
        transformColumns,
        withExpandColumns,
        direction
    ]);
    // ========================== Flatten =========================
    var flattenColumns = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useColumns.useMemo[flattenColumns]": function() {
            if (direction === 'rtl') {
                return revertForRtl(flatColumns(mergedColumns));
            }
            return flatColumns(mergedColumns);
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["useColumns.useMemo[flattenColumns]"], [
        mergedColumns,
        direction,
        scrollWidth
    ]);
    // ========================= Gap Fixed ========================
    var hasGapFixed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useColumns.useMemo[hasGapFixed]": function() {
            // Fixed: left, since old browser not support `findLastIndex`, we should use reverse loop
            var lastLeftIndex = -1;
            for(var i = flattenColumns.length - 1; i >= 0; i -= 1){
                var colFixed = flattenColumns[i].fixed;
                if (colFixed === 'left' || colFixed === true) {
                    lastLeftIndex = i;
                    break;
                }
            }
            if (lastLeftIndex >= 0) {
                for(var _i = 0; _i <= lastLeftIndex; _i += 1){
                    var _colFixed = flattenColumns[_i].fixed;
                    if (_colFixed !== 'left' && _colFixed !== true) {
                        return true;
                    }
                }
            }
            // Fixed: right
            var firstRightIndex = flattenColumns.findIndex({
                "useColumns.useMemo[hasGapFixed].firstRightIndex": function(_ref3) {
                    var colFixed = _ref3.fixed;
                    return colFixed === 'right';
                }
            }["useColumns.useMemo[hasGapFixed].firstRightIndex"]);
            if (firstRightIndex >= 0) {
                for(var _i2 = firstRightIndex; _i2 < flattenColumns.length; _i2 += 1){
                    var _colFixed2 = flattenColumns[_i2].fixed;
                    if (_colFixed2 !== 'right') {
                        return true;
                    }
                }
            }
            return false;
        }
    }["useColumns.useMemo[hasGapFixed]"], [
        flattenColumns
    ]);
    // ========================= FillWidth ========================
    var _useWidthColumns = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useColumns$2f$useWidthColumns$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(flattenColumns, scrollWidth, clientWidth), _useWidthColumns2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_useWidthColumns, 2), filledColumns = _useWidthColumns2[0], realScrollWidth = _useWidthColumns2[1];
    return [
        mergedColumns,
        filledColumns,
        realScrollWidth,
        hasGapFixed
    ];
}
const __TURBOPACK__default__export__ = useColumns;
}}),
"[project]/node_modules/rc-table/es/hooks/useExpand.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useExpand)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/constant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$expandUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/expandUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/legacyUtil.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
function useExpand(props, mergedData, getRowKey) {
    var expandableConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getExpandableProps"])(props);
    var expandIcon = expandableConfig.expandIcon, expandedRowKeys = expandableConfig.expandedRowKeys, defaultExpandedRowKeys = expandableConfig.defaultExpandedRowKeys, defaultExpandAllRows = expandableConfig.defaultExpandAllRows, expandedRowRender = expandableConfig.expandedRowRender, onExpand = expandableConfig.onExpand, onExpandedRowsChange = expandableConfig.onExpandedRowsChange, childrenColumnName = expandableConfig.childrenColumnName;
    var mergedExpandIcon = expandIcon || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$expandUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["renderExpandIcon"];
    var mergedChildrenColumnName = childrenColumnName || 'children';
    var expandableType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useExpand.useMemo[expandableType]": function() {
            if (expandedRowRender) {
                return 'row';
            }
            /* eslint-disable no-underscore-dangle */ /**
     * Fix https://github.com/ant-design/ant-design/issues/21154
     * This is a workaround to not to break current behavior.
     * We can remove follow code after final release.
     *
     * To other developer:
     *  Do not use `__PARENT_RENDER_ICON__` in prod since we will remove this when refactor
     */ if (props.expandable && props.internalHooks === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INTERNAL_HOOKS"] && props.expandable.__PARENT_RENDER_ICON__ || mergedData.some({
                "useExpand.useMemo[expandableType]": function(record) {
                    return record && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(record) === 'object' && record[mergedChildrenColumnName];
                }
            }["useExpand.useMemo[expandableType]"])) {
                return 'nest';
            }
            /* eslint-enable */ return false;
        }
    }["useExpand.useMemo[expandableType]"], [
        !!expandedRowRender,
        mergedData
    ]);
    var _React$useState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "useExpand.useState[_React$useState]": function() {
            if (defaultExpandedRowKeys) {
                return defaultExpandedRowKeys;
            }
            if (defaultExpandAllRows) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$expandUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findAllChildrenKeys"])(mergedData, getRowKey, mergedChildrenColumnName);
            }
            return [];
        }
    }["useExpand.useState[_React$useState]"]), _React$useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState, 2), innerExpandedKeys = _React$useState2[0], setInnerExpandedKeys = _React$useState2[1];
    var mergedExpandedKeys = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useExpand.useMemo[mergedExpandedKeys]": function() {
            return new Set(expandedRowKeys || innerExpandedKeys || []);
        }
    }["useExpand.useMemo[mergedExpandedKeys]"], [
        expandedRowKeys,
        innerExpandedKeys
    ]);
    var onTriggerExpand = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useExpand.useCallback[onTriggerExpand]": function(record) {
            var key = getRowKey(record, mergedData.indexOf(record));
            var newExpandedKeys;
            var hasKey = mergedExpandedKeys.has(key);
            if (hasKey) {
                mergedExpandedKeys.delete(key);
                newExpandedKeys = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(mergedExpandedKeys);
            } else {
                newExpandedKeys = [].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(mergedExpandedKeys), [
                    key
                ]);
            }
            setInnerExpandedKeys(newExpandedKeys);
            if (onExpand) {
                onExpand(!hasKey, record);
            }
            if (onExpandedRowsChange) {
                onExpandedRowsChange(newExpandedKeys);
            }
        }
    }["useExpand.useCallback[onTriggerExpand]"], [
        getRowKey,
        mergedExpandedKeys,
        mergedData,
        onExpand,
        onExpandedRowsChange
    ]);
    // Warning if use `expandedRowRender` and nest children in the same time
    if (("TURBOPACK compile-time value", "development") !== 'production' && expandedRowRender && mergedData.some(function(record) {
        return Array.isArray(record === null || record === void 0 ? void 0 : record[mergedChildrenColumnName]);
    })) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, '`expandedRowRender` should not use with nested Table');
    }
    return [
        expandableConfig,
        expandableType,
        mergedExpandedKeys,
        mergedExpandIcon,
        mergedChildrenColumnName,
        onTriggerExpand
    ];
}
}}),
"[project]/node_modules/rc-table/es/hooks/useFixedInfo.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useFixedInfo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMemo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useMemo.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$isEqual$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/isEqual.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$fixUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/fixUtil.js [app-client] (ecmascript)");
;
;
;
function useFixedInfo(flattenColumns, stickyOffsets, direction) {
    var fixedInfoList = flattenColumns.map(function(_, colIndex) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$fixUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCellFixedInfo"])(colIndex, colIndex, flattenColumns, stickyOffsets, direction);
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMemo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "useFixedInfo.useMemo": function() {
            return fixedInfoList;
        }
    }["useFixedInfo.useMemo"], [
        fixedInfoList
    ], {
        "useFixedInfo.useMemo": function(prev, next) {
            return !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$isEqual$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prev, next);
        }
    }["useFixedInfo.useMemo"]);
}
}}),
"[project]/node_modules/rc-table/es/hooks/useFrame.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useLayoutState": (()=>useLayoutState),
    "useTimeoutLock": (()=>useTimeoutLock)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
function useLayoutState(defaultState) {
    var stateRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(defaultState);
    var _useState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({}), _useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_useState, 2), forceUpdate = _useState2[1];
    var lastPromiseRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    var updateBatchRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    function setFrameState(updater) {
        updateBatchRef.current.push(updater);
        var promise = Promise.resolve();
        lastPromiseRef.current = promise;
        promise.then(function() {
            if (lastPromiseRef.current === promise) {
                var prevBatch = updateBatchRef.current;
                var prevState = stateRef.current;
                updateBatchRef.current = [];
                prevBatch.forEach(function(batchUpdater) {
                    stateRef.current = batchUpdater(stateRef.current);
                });
                lastPromiseRef.current = null;
                if (prevState !== stateRef.current) {
                    forceUpdate({});
                }
            }
        });
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useLayoutState.useEffect": function() {
            return ({
                "useLayoutState.useEffect": function() {
                    lastPromiseRef.current = null;
                }
            })["useLayoutState.useEffect"];
        }
    }["useLayoutState.useEffect"], []);
    return [
        stateRef.current,
        setFrameState
    ];
}
function useTimeoutLock(defaultState) {
    var frameRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(defaultState || null);
    var timeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    function cleanUp() {
        window.clearTimeout(timeoutRef.current);
    }
    function setState(newState) {
        frameRef.current = newState;
        cleanUp();
        timeoutRef.current = window.setTimeout(function() {
            frameRef.current = null;
            timeoutRef.current = undefined;
        }, 100);
    }
    function getState() {
        return frameRef.current;
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useTimeoutLock.useEffect": function() {
            return cleanUp;
        }
    }["useTimeoutLock.useEffect"], []);
    return [
        setState,
        getState
    ];
}
}}),
"[project]/node_modules/rc-table/es/hooks/useHover.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useHover)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
function useHover() {
    var _React$useState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(-1), _React$useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState, 2), startRow = _React$useState2[0], setStartRow = _React$useState2[1];
    var _React$useState3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(-1), _React$useState4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState3, 2), endRow = _React$useState4[0], setEndRow = _React$useState4[1];
    var onHover = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useHover.useCallback[onHover]": function(start, end) {
            setStartRow(start);
            setEndRow(end);
        }
    }["useHover.useCallback[onHover]"], []);
    return [
        startRow,
        endRow,
        onHover
    ];
}
}}),
"[project]/node_modules/rc-table/es/hooks/useSticky.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useSticky)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$canUseDom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/canUseDom.js [app-client] (ecmascript)");
;
;
;
// fix ssr render
var defaultContainer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$canUseDom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])() ? window : null;
function useSticky(sticky, prefixCls) {
    var _ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(sticky) === 'object' ? sticky : {}, _ref$offsetHeader = _ref.offsetHeader, offsetHeader = _ref$offsetHeader === void 0 ? 0 : _ref$offsetHeader, _ref$offsetSummary = _ref.offsetSummary, offsetSummary = _ref$offsetSummary === void 0 ? 0 : _ref$offsetSummary, _ref$offsetScroll = _ref.offsetScroll, offsetScroll = _ref$offsetScroll === void 0 ? 0 : _ref$offsetScroll, _ref$getContainer = _ref.getContainer, getContainer = _ref$getContainer === void 0 ? function() {
        return defaultContainer;
    } : _ref$getContainer;
    var container = getContainer() || defaultContainer;
    var isSticky = !!sticky;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useSticky.useMemo": function() {
            return {
                isSticky: isSticky,
                stickyClassName: isSticky ? "".concat(prefixCls, "-sticky-holder") : '',
                offsetHeader: offsetHeader,
                offsetSummary: offsetSummary,
                offsetScroll: offsetScroll,
                container: container
            };
        }
    }["useSticky.useMemo"], [
        isSticky,
        offsetScroll,
        offsetHeader,
        offsetSummary,
        prefixCls,
        container
    ]);
}
}}),
"[project]/node_modules/rc-table/es/hooks/useStickyOffsets.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
/**
 * Get sticky column offset width
 */ function useStickyOffsets(colWidths, flattenColumns, direction) {
    var stickyOffsets = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useStickyOffsets.useMemo[stickyOffsets]": function() {
            var columnCount = flattenColumns.length;
            var getOffsets = function getOffsets(startIndex, endIndex, offset) {
                var offsets = [];
                var total = 0;
                for(var i = startIndex; i !== endIndex; i += offset){
                    offsets.push(total);
                    if (flattenColumns[i].fixed) {
                        total += colWidths[i] || 0;
                    }
                }
                return offsets;
            };
            var startOffsets = getOffsets(0, columnCount, 1);
            var endOffsets = getOffsets(columnCount - 1, -1, -1).reverse();
            return direction === 'rtl' ? {
                left: endOffsets,
                right: startOffsets
            } : {
                left: startOffsets,
                right: endOffsets
            };
        }
    }["useStickyOffsets.useMemo[stickyOffsets]"], [
        colWidths,
        flattenColumns,
        direction
    ]);
    return stickyOffsets;
}
const __TURBOPACK__default__export__ = useStickyOffsets;
}}),
"[project]/node_modules/rc-table/es/Panel/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function Panel(_ref) {
    var className = _ref.className, children = _ref.children;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: className
    }, children);
}
const __TURBOPACK__default__export__ = Panel;
}}),
"[project]/node_modules/rc-table/es/utils/offsetUtil.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getOffset": (()=>getOffset)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/findDOMNode.js [app-client] (ecmascript)");
;
function getOffset(node) {
    var element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDOM"])(node);
    var box = element.getBoundingClientRect();
    var docElem = document.documentElement;
    // < ie8 not support win.pageXOffset, use docElem.scrollLeft instead
    return {
        left: box.left + (window.pageXOffset || docElem.scrollLeft) - (docElem.clientLeft || document.body.clientLeft || 0),
        top: box.top + (window.pageYOffset || docElem.scrollTop) - (docElem.clientTop || document.body.clientTop || 0)
    };
}
}}),
"[project]/node_modules/rc-table/es/stickyScrollBar.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/defineProperty.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$addEventListener$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/addEventListener.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$getScrollBarSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/getScrollBarSize.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useFrame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useFrame.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$raf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/raf.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$offsetUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/offsetUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/findDOMNode.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
var StickyScrollBar = function StickyScrollBar(_ref, ref) {
    var _scrollBodyRef$curren, _scrollBodyRef$curren2;
    var scrollBodyRef = _ref.scrollBodyRef, onScroll = _ref.onScroll, offsetScroll = _ref.offsetScroll, container = _ref.container, direction = _ref.direction;
    var prefixCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], 'prefixCls');
    var bodyScrollWidth = ((_scrollBodyRef$curren = scrollBodyRef.current) === null || _scrollBodyRef$curren === void 0 ? void 0 : _scrollBodyRef$curren.scrollWidth) || 0;
    var bodyWidth = ((_scrollBodyRef$curren2 = scrollBodyRef.current) === null || _scrollBodyRef$curren2 === void 0 ? void 0 : _scrollBodyRef$curren2.clientWidth) || 0;
    var scrollBarWidth = bodyScrollWidth && bodyWidth * (bodyWidth / bodyScrollWidth);
    var scrollBarRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    var _useLayoutState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useFrame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutState"])({
        scrollLeft: 0,
        isHiddenScrollBar: true
    }), _useLayoutState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_useLayoutState, 2), scrollState = _useLayoutState2[0], setScrollState = _useLayoutState2[1];
    var refState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({
        delta: 0,
        x: 0
    });
    var _React$useState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false), _React$useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState, 2), isActive = _React$useState2[0], setActive = _React$useState2[1];
    var rafRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "StickyScrollBar.useEffect": function() {
            return ({
                "StickyScrollBar.useEffect": function() {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$raf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].cancel(rafRef.current);
                }
            })["StickyScrollBar.useEffect"];
        }
    }["StickyScrollBar.useEffect"], []);
    var onMouseUp = function onMouseUp() {
        setActive(false);
    };
    var onMouseDown = function onMouseDown(event) {
        event.persist();
        refState.current.delta = event.pageX - scrollState.scrollLeft;
        refState.current.x = 0;
        setActive(true);
        event.preventDefault();
    };
    var onMouseMove = function onMouseMove(event) {
        var _window;
        // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons
        var _ref2 = event || ((_window = window) === null || _window === void 0 ? void 0 : _window.event), buttons = _ref2.buttons;
        if (!isActive || buttons === 0) {
            // If out body mouse up, we can set isActive false when mouse move
            if (isActive) {
                setActive(false);
            }
            return;
        }
        var left = refState.current.x + event.pageX - refState.current.x - refState.current.delta;
        var isRTL = direction === 'rtl';
        // Limit scroll range
        left = Math.max(isRTL ? scrollBarWidth - bodyWidth : 0, Math.min(isRTL ? 0 : bodyWidth - scrollBarWidth, left));
        // Calculate the scroll position and update
        var shouldScroll = !isRTL || Math.abs(left) + Math.abs(scrollBarWidth) < bodyWidth;
        if (shouldScroll) {
            onScroll({
                scrollLeft: left / bodyWidth * (bodyScrollWidth + 2)
            });
            refState.current.x = event.pageX;
        }
    };
    var checkScrollBarVisible = function checkScrollBarVisible() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$raf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].cancel(rafRef.current);
        rafRef.current = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$raf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(function() {
            if (!scrollBodyRef.current) {
                return;
            }
            var tableOffsetTop = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$offsetUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOffset"])(scrollBodyRef.current).top;
            var tableBottomOffset = tableOffsetTop + scrollBodyRef.current.offsetHeight;
            var currentClientOffset = container === window ? document.documentElement.scrollTop + window.innerHeight : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$offsetUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOffset"])(container).top + container.clientHeight;
            if (tableBottomOffset - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$getScrollBarSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])() <= currentClientOffset || tableOffsetTop >= currentClientOffset - offsetScroll) {
                setScrollState(function(state) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, state), {}, {
                        isHiddenScrollBar: true
                    });
                });
            } else {
                setScrollState(function(state) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, state), {}, {
                        isHiddenScrollBar: false
                    });
                });
            }
        });
    };
    var setScrollLeft = function setScrollLeft(left) {
        setScrollState(function(state) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, state), {}, {
                scrollLeft: left / bodyScrollWidth * bodyWidth || 0
            });
        });
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "StickyScrollBar.useImperativeHandle": function() {
            return {
                setScrollLeft: setScrollLeft,
                checkScrollBarVisible: checkScrollBarVisible
            };
        }
    }["StickyScrollBar.useImperativeHandle"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "StickyScrollBar.useEffect": function() {
            var onMouseUpListener = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$addEventListener$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(document.body, 'mouseup', onMouseUp, false);
            var onMouseMoveListener = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$addEventListener$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(document.body, 'mousemove', onMouseMove, false);
            checkScrollBarVisible();
            return ({
                "StickyScrollBar.useEffect": function() {
                    onMouseUpListener.remove();
                    onMouseMoveListener.remove();
                }
            })["StickyScrollBar.useEffect"];
        }
    }["StickyScrollBar.useEffect"], [
        scrollBarWidth,
        isActive
    ]);
    // Loop for scroll event check
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "StickyScrollBar.useEffect": function() {
            if (!scrollBodyRef.current) return;
            var scrollParents = [];
            var parent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDOM"])(scrollBodyRef.current);
            while(parent){
                scrollParents.push(parent);
                parent = parent.parentElement;
            }
            scrollParents.forEach({
                "StickyScrollBar.useEffect": function(p) {
                    return p.addEventListener('scroll', checkScrollBarVisible, false);
                }
            }["StickyScrollBar.useEffect"]);
            window.addEventListener('resize', checkScrollBarVisible, false);
            window.addEventListener('scroll', checkScrollBarVisible, false);
            container.addEventListener('scroll', checkScrollBarVisible, false);
            return ({
                "StickyScrollBar.useEffect": function() {
                    scrollParents.forEach({
                        "StickyScrollBar.useEffect": function(p) {
                            return p.removeEventListener('scroll', checkScrollBarVisible);
                        }
                    }["StickyScrollBar.useEffect"]);
                    window.removeEventListener('resize', checkScrollBarVisible);
                    window.removeEventListener('scroll', checkScrollBarVisible);
                    container.removeEventListener('scroll', checkScrollBarVisible);
                }
            })["StickyScrollBar.useEffect"];
        }
    }["StickyScrollBar.useEffect"], [
        container
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "StickyScrollBar.useEffect": function() {
            if (!scrollState.isHiddenScrollBar) {
                setScrollState({
                    "StickyScrollBar.useEffect": function(state) {
                        var bodyNode = scrollBodyRef.current;
                        if (!bodyNode) {
                            return state;
                        }
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, state), {}, {
                            scrollLeft: bodyNode.scrollLeft / bodyNode.scrollWidth * bodyNode.clientWidth
                        });
                    }
                }["StickyScrollBar.useEffect"]);
            }
        }
    }["StickyScrollBar.useEffect"], [
        scrollState.isHiddenScrollBar
    ]);
    if (bodyScrollWidth <= bodyWidth || !scrollBarWidth || scrollState.isHiddenScrollBar) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        style: {
            height: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$getScrollBarSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(),
            width: bodyWidth,
            bottom: offsetScroll
        },
        className: "".concat(prefixCls, "-sticky-scroll")
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        onMouseDown: onMouseDown,
        ref: scrollBarRef,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("".concat(prefixCls, "-sticky-scroll-bar"), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, "".concat(prefixCls, "-sticky-scroll-bar-active"), isActive)),
        style: {
            width: "".concat(scrollBarWidth, "px"),
            transform: "translate3d(".concat(scrollState.scrollLeft, "px, 0, 0)")
        }
    }));
};
const __TURBOPACK__default__export__ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(StickyScrollBar);
}}),
"[project]/node_modules/rc-table/es/Table.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DEFAULT_PREFIX": (()=>DEFAULT_PREFIX),
    "default": (()=>__TURBOPACK__default__export__),
    "genTable": (()=>genTable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/defineProperty.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
/**
 * Feature:
 *  - fixed not need to set width
 *  - support `rowExpandable` to config row expand logic
 *  - add `summary` to support `() => ReactNode`
 *
 * Update:
 *  - `dataIndex` is `array[]` now
 *  - `expandable` wrap all the expand related props
 *
 * Removed:
 *  - expandIconAsCell
 *  - useFixedHeader
 *  - rowRef
 *  - columns[number].onCellClick
 *  - onRowClick
 *  - onRowDoubleClick
 *  - onRowMouseEnter
 *  - onRowMouseLeave
 *  - getBodyWrapper
 *  - bodyStyle
 *
 * Deprecated:
 *  - All expanded props, move into expandable
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-resize-observer/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-resize-observer/es/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$styleChecker$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/styleChecker.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$getScrollBarSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/getScrollBarSize.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useEvent.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$pickAttrs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/pickAttrs.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$utils$2f$get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/utils/get.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Body/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$ColGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/ColGroup.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/constant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$FixedHolder$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/FixedHolder/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Footer/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$Summary$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Footer/Summary.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Header$2f$Header$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Header/Header.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useColumns$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useColumns/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useExpand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useExpand.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useFixedInfo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useFixedInfo.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useFrame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useFrame.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useHover$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useHover.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useSticky$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useSticky.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useStickyOffsets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useStickyOffsets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Panel$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Panel/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$stickyScrollBar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/stickyScrollBar.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$sugar$2f$Column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/sugar/Column.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$sugar$2f$ColumnGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/sugar/ColumnGroup.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/valueUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/findDOMNode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useLayoutEffect.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
var DEFAULT_PREFIX = 'rc-table';
// Used for conditions cache
var EMPTY_DATA = [];
// Used for customize scroll
var EMPTY_SCROLL_TARGET = {};
function defaultEmpty() {
    return 'No Data';
}
function Table(tableProps, ref) {
    var props = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        rowKey: 'key',
        prefixCls: DEFAULT_PREFIX,
        emptyText: defaultEmpty
    }, tableProps);
    var prefixCls = props.prefixCls, className = props.className, rowClassName = props.rowClassName, style = props.style, data = props.data, rowKey = props.rowKey, scroll = props.scroll, tableLayout = props.tableLayout, direction = props.direction, title = props.title, footer = props.footer, summary = props.summary, caption = props.caption, id = props.id, showHeader = props.showHeader, components = props.components, emptyText = props.emptyText, onRow = props.onRow, onHeaderRow = props.onHeaderRow, onScroll = props.onScroll, internalHooks = props.internalHooks, transformColumns = props.transformColumns, internalRefs = props.internalRefs, tailor = props.tailor, getContainerWidth = props.getContainerWidth, sticky = props.sticky, _props$rowHoverable = props.rowHoverable, rowHoverable = _props$rowHoverable === void 0 ? true : _props$rowHoverable;
    var mergedData = data || EMPTY_DATA;
    var hasData = !!mergedData.length;
    var useInternalHooks = internalHooks === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INTERNAL_HOOKS"];
    // ===================== Warning ======================
    if ("TURBOPACK compile-time truthy", 1) {
        [
            'onRowClick',
            'onRowDoubleClick',
            'onRowContextMenu',
            'onRowMouseEnter',
            'onRowMouseLeave'
        ].forEach(function(name) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props[name] === undefined, "`".concat(name, "` is removed, please use `onRow` instead."));
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(!('getBodyWrapper' in props), '`getBodyWrapper` is deprecated, please use custom `components` instead.');
    }
    // ==================== Customize =====================
    var getComponent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Table.useCallback[getComponent]": function(path, defaultComponent) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$utils$2f$get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(components, path) || defaultComponent;
        }
    }["Table.useCallback[getComponent]"], [
        components
    ]);
    var getRowKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Table.useMemo[getRowKey]": function() {
            if (typeof rowKey === 'function') {
                return rowKey;
            }
            return ({
                "Table.useMemo[getRowKey]": function(record) {
                    var key = record && record[rowKey];
                    if ("TURBOPACK compile-time truthy", 1) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(key !== undefined, 'Each record in table should have a unique `key` prop, or set `rowKey` to an unique primary key.');
                    }
                    return key;
                }
            })["Table.useMemo[getRowKey]"];
        }
    }["Table.useMemo[getRowKey]"], [
        rowKey
    ]);
    var customizeScrollBody = getComponent([
        'body'
    ]);
    // ====================== Hover =======================
    var _useHover = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useHover$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(), _useHover2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_useHover, 3), startRow = _useHover2[0], endRow = _useHover2[1], onHover = _useHover2[2];
    // ====================== Expand ======================
    var _useExpand = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useExpand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, mergedData, getRowKey), _useExpand2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_useExpand, 6), expandableConfig = _useExpand2[0], expandableType = _useExpand2[1], mergedExpandedKeys = _useExpand2[2], mergedExpandIcon = _useExpand2[3], mergedChildrenColumnName = _useExpand2[4], onTriggerExpand = _useExpand2[5];
    // ====================== Column ======================
    var scrollX = scroll === null || scroll === void 0 ? void 0 : scroll.x;
    var _React$useState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0), _React$useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState, 2), componentWidth = _React$useState2[0], setComponentWidth = _React$useState2[1];
    var _useColumns = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useColumns$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, props), expandableConfig), {}, {
        expandable: !!expandableConfig.expandedRowRender,
        columnTitle: expandableConfig.columnTitle,
        expandedKeys: mergedExpandedKeys,
        getRowKey: getRowKey,
        // https://github.com/ant-design/ant-design/issues/23894
        onTriggerExpand: onTriggerExpand,
        expandIcon: mergedExpandIcon,
        expandIconColumnIndex: expandableConfig.expandIconColumnIndex,
        direction: direction,
        scrollWidth: useInternalHooks && tailor && typeof scrollX === 'number' ? scrollX : null,
        clientWidth: componentWidth
    }), useInternalHooks ? transformColumns : null), _useColumns2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_useColumns, 4), columns = _useColumns2[0], flattenColumns = _useColumns2[1], flattenScrollX = _useColumns2[2], hasGapFixed = _useColumns2[3];
    var mergedScrollX = flattenScrollX !== null && flattenScrollX !== void 0 ? flattenScrollX : scrollX;
    var columnContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Table.useMemo[columnContext]": function() {
            return {
                columns: columns,
                flattenColumns: flattenColumns
            };
        }
    }["Table.useMemo[columnContext]"], [
        columns,
        flattenColumns
    ]);
    // ======================= Refs =======================
    var fullTableRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    var scrollHeaderRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    var scrollBodyRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    var scrollBodyContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "Table.useImperativeHandle": function() {
            return {
                nativeElement: fullTableRef.current,
                scrollTo: function scrollTo(config) {
                    var _scrollBodyRef$curren3;
                    if (scrollBodyRef.current instanceof HTMLElement) {
                        // Native scroll
                        var index = config.index, top = config.top, key = config.key;
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validNumberValue"])(top)) {
                            var _scrollBodyRef$curren;
                            (_scrollBodyRef$curren = scrollBodyRef.current) === null || _scrollBodyRef$curren === void 0 || _scrollBodyRef$curren.scrollTo({
                                top: top
                            });
                        } else {
                            var _scrollBodyRef$curren2;
                            var mergedKey = key !== null && key !== void 0 ? key : getRowKey(mergedData[index]);
                            (_scrollBodyRef$curren2 = scrollBodyRef.current.querySelector("[data-row-key=\"".concat(mergedKey, "\"]"))) === null || _scrollBodyRef$curren2 === void 0 || _scrollBodyRef$curren2.scrollIntoView();
                        }
                    } else if ((_scrollBodyRef$curren3 = scrollBodyRef.current) !== null && _scrollBodyRef$curren3 !== void 0 && _scrollBodyRef$curren3.scrollTo) {
                        // Pass to proxy
                        scrollBodyRef.current.scrollTo(config);
                    }
                }
            };
        }
    }["Table.useImperativeHandle"]);
    // ====================== Scroll ======================
    var scrollSummaryRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    var _React$useState3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false), _React$useState4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState3, 2), pingedLeft = _React$useState4[0], setPingedLeft = _React$useState4[1];
    var _React$useState5 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false), _React$useState6 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState5, 2), pingedRight = _React$useState6[0], setPingedRight = _React$useState6[1];
    var _React$useState7 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Map()), _React$useState8 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState7, 2), colsWidths = _React$useState8[0], updateColsWidths = _React$useState8[1];
    // Convert map to number width
    var colsKeys = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getColumnsKey"])(flattenColumns);
    var pureColWidths = colsKeys.map(function(columnKey) {
        return colsWidths.get(columnKey);
    });
    var colWidths = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Table.useMemo[colWidths]": function() {
            return pureColWidths;
        }
    }["Table.useMemo[colWidths]"], [
        pureColWidths.join('_')
    ]);
    var stickyOffsets = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useStickyOffsets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(colWidths, flattenColumns, direction);
    var fixHeader = scroll && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateValue"])(scroll.y);
    var horizonScroll = scroll && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateValue"])(mergedScrollX) || Boolean(expandableConfig.fixed);
    var fixColumn = horizonScroll && flattenColumns.some(function(_ref) {
        var fixed = _ref.fixed;
        return fixed;
    });
    // Sticky
    var stickyRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    var _useSticky = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useSticky$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(sticky, prefixCls), isSticky = _useSticky.isSticky, offsetHeader = _useSticky.offsetHeader, offsetSummary = _useSticky.offsetSummary, offsetScroll = _useSticky.offsetScroll, stickyClassName = _useSticky.stickyClassName, container = _useSticky.container;
    // Footer (Fix footer must fixed header)
    var summaryNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Table.useMemo[summaryNode]": function() {
            return summary === null || summary === void 0 ? void 0 : summary(mergedData);
        }
    }["Table.useMemo[summaryNode]"], [
        summary,
        mergedData
    ]);
    var fixFooter = (fixHeader || isSticky) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(summaryNode) && summaryNode.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$Summary$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] && summaryNode.props.fixed;
    // Scroll
    var scrollXStyle;
    var scrollYStyle;
    var scrollTableStyle;
    if (fixHeader) {
        scrollYStyle = {
            overflowY: hasData ? 'scroll' : 'auto',
            maxHeight: scroll.y
        };
    }
    if (horizonScroll) {
        scrollXStyle = {
            overflowX: 'auto'
        };
        // When no vertical scrollbar, should hide it
        // https://github.com/ant-design/ant-design/pull/20705
        // https://github.com/ant-design/ant-design/issues/21879
        if (!fixHeader) {
            scrollYStyle = {
                overflowY: 'hidden'
            };
        }
        scrollTableStyle = {
            width: mergedScrollX === true ? 'auto' : mergedScrollX,
            minWidth: '100%'
        };
    }
    var onColumnResize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Table.useCallback[onColumnResize]": function(columnKey, width) {
            updateColsWidths({
                "Table.useCallback[onColumnResize]": function(widths) {
                    if (widths.get(columnKey) !== width) {
                        var newWidths = new Map(widths);
                        newWidths.set(columnKey, width);
                        return newWidths;
                    }
                    return widths;
                }
            }["Table.useCallback[onColumnResize]"]);
        }
    }["Table.useCallback[onColumnResize]"], []);
    var _useTimeoutLock = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useFrame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTimeoutLock"])(null), _useTimeoutLock2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_useTimeoutLock, 2), setScrollTarget = _useTimeoutLock2[0], getScrollTarget = _useTimeoutLock2[1];
    function forceScroll(scrollLeft, target) {
        if (!target) {
            return;
        }
        if (typeof target === 'function') {
            target(scrollLeft);
        } else if (target.scrollLeft !== scrollLeft) {
            target.scrollLeft = scrollLeft;
            // Delay to force scroll position if not sync
            // ref: https://github.com/ant-design/ant-design/issues/37179
            if (target.scrollLeft !== scrollLeft) {
                setTimeout(function() {
                    target.scrollLeft = scrollLeft;
                }, 0);
            }
        }
    }
    var onInternalScroll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "Table.useEvent[onInternalScroll]": function(_ref2) {
            var currentTarget = _ref2.currentTarget, scrollLeft = _ref2.scrollLeft;
            var isRTL = direction === 'rtl';
            var mergedScrollLeft = typeof scrollLeft === 'number' ? scrollLeft : currentTarget.scrollLeft;
            var compareTarget = currentTarget || EMPTY_SCROLL_TARGET;
            if (!getScrollTarget() || getScrollTarget() === compareTarget) {
                var _stickyRef$current;
                setScrollTarget(compareTarget);
                forceScroll(mergedScrollLeft, scrollHeaderRef.current);
                forceScroll(mergedScrollLeft, scrollBodyRef.current);
                forceScroll(mergedScrollLeft, scrollSummaryRef.current);
                forceScroll(mergedScrollLeft, (_stickyRef$current = stickyRef.current) === null || _stickyRef$current === void 0 ? void 0 : _stickyRef$current.setScrollLeft);
            }
            var measureTarget = currentTarget || scrollHeaderRef.current;
            if (measureTarget) {
                var scrollWidth = // Should use mergedScrollX in virtual table(useInternalHooks && tailor === true)
                useInternalHooks && tailor && typeof mergedScrollX === 'number' ? mergedScrollX : measureTarget.scrollWidth;
                var clientWidth = measureTarget.clientWidth;
                // There is no space to scroll
                if (scrollWidth === clientWidth) {
                    setPingedLeft(false);
                    setPingedRight(false);
                    return;
                }
                if (isRTL) {
                    setPingedLeft(-mergedScrollLeft < scrollWidth - clientWidth);
                    setPingedRight(-mergedScrollLeft > 0);
                } else {
                    setPingedLeft(mergedScrollLeft > 0);
                    setPingedRight(mergedScrollLeft < scrollWidth - clientWidth);
                }
            }
        }
    }["Table.useEvent[onInternalScroll]"]);
    var onBodyScroll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "Table.useEvent[onBodyScroll]": function(e) {
            onInternalScroll(e);
            onScroll === null || onScroll === void 0 || onScroll(e);
        }
    }["Table.useEvent[onBodyScroll]"]);
    var triggerOnScroll = function triggerOnScroll() {
        if (horizonScroll && scrollBodyRef.current) {
            var _scrollBodyRef$curren4;
            onInternalScroll({
                currentTarget: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDOM"])(scrollBodyRef.current),
                scrollLeft: (_scrollBodyRef$curren4 = scrollBodyRef.current) === null || _scrollBodyRef$curren4 === void 0 ? void 0 : _scrollBodyRef$curren4.scrollLeft
            });
        } else {
            setPingedLeft(false);
            setPingedRight(false);
        }
    };
    var onFullTableResize = function onFullTableResize(_ref3) {
        var _stickyRef$current2;
        var width = _ref3.width;
        (_stickyRef$current2 = stickyRef.current) === null || _stickyRef$current2 === void 0 || _stickyRef$current2.checkScrollBarVisible();
        var mergedWidth = fullTableRef.current ? fullTableRef.current.offsetWidth : width;
        if (useInternalHooks && getContainerWidth && fullTableRef.current) {
            mergedWidth = getContainerWidth(fullTableRef.current, mergedWidth) || mergedWidth;
        }
        if (mergedWidth !== componentWidth) {
            triggerOnScroll();
            setComponentWidth(mergedWidth);
        }
    };
    // Sync scroll bar when init or `horizonScroll`, `data` and `columns.length` changed
    var mounted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Table.useEffect": function() {
            // onFullTableResize will be trigger once when ResizeObserver is mounted
            // This will reduce one duplicated triggerOnScroll time
            if (mounted.current) {
                triggerOnScroll();
            }
        }
    }["Table.useEffect"], [
        horizonScroll,
        data,
        columns.length
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Table.useEffect": function() {
            mounted.current = true;
        }
    }["Table.useEffect"], []);
    // ===================== Effects ======================
    var _React$useState9 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0), _React$useState10 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState9, 2), scrollbarSize = _React$useState10[0], setScrollbarSize = _React$useState10[1];
    var _React$useState11 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true), _React$useState12 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState11, 2), supportSticky = _React$useState12[0], setSupportSticky = _React$useState12[1]; // Only IE not support, we mark as support first
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "Table.useLayoutEffect": function() {
            if (!tailor || !useInternalHooks) {
                if (scrollBodyRef.current instanceof Element) {
                    setScrollbarSize((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$getScrollBarSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTargetScrollBarSize"])(scrollBodyRef.current).width);
                } else {
                    setScrollbarSize((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$getScrollBarSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTargetScrollBarSize"])(scrollBodyContainerRef.current).width);
                }
            }
            setSupportSticky((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$styleChecker$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isStyleSupport"])('position', 'sticky'));
        }
    }["Table.useLayoutEffect"], []);
    // ================== INTERNAL HOOKS ==================
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Table.useEffect": function() {
            if (useInternalHooks && internalRefs) {
                internalRefs.body.current = scrollBodyRef.current;
            }
        }
    }["Table.useEffect"]);
    // ========================================================================
    // ==                               Render                               ==
    // ========================================================================
    // =================== Render: Func ===================
    var renderFixedHeaderTable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Table.useCallback[renderFixedHeaderTable]": function(fixedHolderPassProps) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Header$2f$Header$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], fixedHolderPassProps), fixFooter === 'top' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], fixedHolderPassProps, summaryNode));
        }
    }["Table.useCallback[renderFixedHeaderTable]"], [
        fixFooter,
        summaryNode
    ]);
    var renderFixedFooterTable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Table.useCallback[renderFixedFooterTable]": function(fixedHolderPassProps) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], fixedHolderPassProps, summaryNode);
        }
    }["Table.useCallback[renderFixedFooterTable]"], [
        summaryNode
    ]);
    // =================== Render: Node ===================
    var TableComponent = getComponent([
        'table'
    ], 'table');
    // Table layout
    var mergedTableLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Table.useMemo[mergedTableLayout]": function() {
            if (tableLayout) {
                return tableLayout;
            }
            // https://github.com/ant-design/ant-design/issues/25227
            // When scroll.x is max-content, no need to fix table layout
            // it's width should stretch out to fit content
            if (fixColumn) {
                return mergedScrollX === 'max-content' ? 'auto' : 'fixed';
            }
            if (fixHeader || isSticky || flattenColumns.some({
                "Table.useMemo[mergedTableLayout]": function(_ref4) {
                    var ellipsis = _ref4.ellipsis;
                    return ellipsis;
                }
            }["Table.useMemo[mergedTableLayout]"])) {
                return 'fixed';
            }
            return 'auto';
        }
    }["Table.useMemo[mergedTableLayout]"], [
        fixHeader,
        fixColumn,
        flattenColumns,
        tableLayout,
        isSticky
    ]);
    var groupTableNode;
    // Header props
    var headerProps = {
        colWidths: colWidths,
        columCount: flattenColumns.length,
        stickyOffsets: stickyOffsets,
        onHeaderRow: onHeaderRow,
        fixHeader: fixHeader,
        scroll: scroll
    };
    // Empty
    var emptyNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Table.useMemo[emptyNode]": function() {
            if (hasData) {
                return null;
            }
            if (typeof emptyText === 'function') {
                return emptyText();
            }
            return emptyText;
        }
    }["Table.useMemo[emptyNode]"], [
        hasData,
        emptyText
    ]);
    // Body
    var bodyTable = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        data: mergedData,
        measureColumnWidth: fixHeader || horizonScroll || isSticky
    });
    var bodyColGroup = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$ColGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        colWidths: flattenColumns.map(function(_ref5) {
            var width = _ref5.width;
            return width;
        }),
        columns: flattenColumns
    });
    var captionElement = caption !== null && caption !== undefined ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("caption", {
        className: "".concat(prefixCls, "-caption")
    }, caption) : undefined;
    var dataProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$pickAttrs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, {
        data: true
    });
    var ariaProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$pickAttrs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, {
        aria: true
    });
    if (fixHeader || isSticky) {
        // >>>>>> Fixed Header
        var bodyContent;
        if (typeof customizeScrollBody === 'function') {
            bodyContent = customizeScrollBody(mergedData, {
                scrollbarSize: scrollbarSize,
                ref: scrollBodyRef,
                onScroll: onInternalScroll
            });
            headerProps.colWidths = flattenColumns.map(function(_ref6, index) {
                var width = _ref6.width;
                var colWidth = index === flattenColumns.length - 1 ? width - scrollbarSize : width;
                if (typeof colWidth === 'number' && !Number.isNaN(colWidth)) {
                    return colWidth;
                }
                if ("TURBOPACK compile-time truthy", 1) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props.columns.length === 0, 'When use `components.body` with render props. Each column should have a fixed `width` value.');
                }
                return 0;
            });
        } else {
            bodyContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
                style: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, scrollXStyle), scrollYStyle),
                onScroll: onBodyScroll,
                ref: scrollBodyRef,
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("".concat(prefixCls, "-body"))
            }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(TableComponent, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                style: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, scrollTableStyle), {}, {
                    tableLayout: mergedTableLayout
                })
            }, ariaProps), captionElement, bodyColGroup, bodyTable, !fixFooter && summaryNode && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                stickyOffsets: stickyOffsets,
                flattenColumns: flattenColumns
            }, summaryNode)));
        }
        // Fixed holder share the props
        var fixedHolderProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
            noData: !mergedData.length,
            maxContentScroll: horizonScroll && mergedScrollX === 'max-content'
        }, headerProps), columnContext), {}, {
            direction: direction,
            stickyClassName: stickyClassName,
            onScroll: onInternalScroll
        });
        groupTableNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, showHeader !== false && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$FixedHolder$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, fixedHolderProps, {
            stickyTopOffset: offsetHeader,
            className: "".concat(prefixCls, "-header"),
            ref: scrollHeaderRef
        }), renderFixedHeaderTable), bodyContent, fixFooter && fixFooter !== 'top' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$FixedHolder$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, fixedHolderProps, {
            stickyBottomOffset: offsetSummary,
            className: "".concat(prefixCls, "-summary"),
            ref: scrollSummaryRef
        }), renderFixedFooterTable), isSticky && scrollBodyRef.current && scrollBodyRef.current instanceof Element && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$stickyScrollBar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            ref: stickyRef,
            offsetScroll: offsetScroll,
            scrollBodyRef: scrollBodyRef,
            onScroll: onInternalScroll,
            container: container,
            direction: direction
        }));
    } else {
        // >>>>>> Unique table
        groupTableNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
            style: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, scrollXStyle), scrollYStyle),
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("".concat(prefixCls, "-content")),
            onScroll: onInternalScroll,
            ref: scrollBodyRef
        }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(TableComponent, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
            style: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, scrollTableStyle), {}, {
                tableLayout: mergedTableLayout
            })
        }, ariaProps), captionElement, bodyColGroup, showHeader !== false && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Header$2f$Header$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, headerProps, columnContext)), bodyTable, summaryNode && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            stickyOffsets: stickyOffsets,
            flattenColumns: flattenColumns
        }, summaryNode)));
    }
    var fullTable = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, className, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, "".concat(prefixCls, "-rtl"), direction === 'rtl'), "".concat(prefixCls, "-ping-left"), pingedLeft), "".concat(prefixCls, "-ping-right"), pingedRight), "".concat(prefixCls, "-layout-fixed"), tableLayout === 'fixed'), "".concat(prefixCls, "-fixed-header"), fixHeader), "".concat(prefixCls, "-fixed-column"), fixColumn), "".concat(prefixCls, "-fixed-column-gapped"), fixColumn && hasGapFixed), "".concat(prefixCls, "-scroll-horizontal"), horizonScroll), "".concat(prefixCls, "-has-fix-left"), flattenColumns[0] && flattenColumns[0].fixed), "".concat(prefixCls, "-has-fix-right"), flattenColumns[flattenColumns.length - 1] && flattenColumns[flattenColumns.length - 1].fixed === 'right')),
        style: style,
        id: id,
        ref: fullTableRef
    }, dataProps), title && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Panel$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        className: "".concat(prefixCls, "-title")
    }, title(mergedData)), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        ref: scrollBodyContainerRef,
        className: "".concat(prefixCls, "-container")
    }, groupTableNode), footer && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Panel$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        className: "".concat(prefixCls, "-footer")
    }, footer(mergedData)));
    if (horizonScroll) {
        fullTable = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], {
            onResize: onFullTableResize
        }, fullTable);
    }
    var fixedInfoList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useFixedInfo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(flattenColumns, stickyOffsets, direction);
    var TableContextValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Table.useMemo[TableContextValue]": function() {
            return {
                // Scroll
                scrollX: mergedScrollX,
                // Table
                prefixCls: prefixCls,
                getComponent: getComponent,
                scrollbarSize: scrollbarSize,
                direction: direction,
                fixedInfoList: fixedInfoList,
                isSticky: isSticky,
                supportSticky: supportSticky,
                componentWidth: componentWidth,
                fixHeader: fixHeader,
                fixColumn: fixColumn,
                horizonScroll: horizonScroll,
                // Body
                tableLayout: mergedTableLayout,
                rowClassName: rowClassName,
                expandedRowClassName: expandableConfig.expandedRowClassName,
                expandIcon: mergedExpandIcon,
                expandableType: expandableType,
                expandRowByClick: expandableConfig.expandRowByClick,
                expandedRowRender: expandableConfig.expandedRowRender,
                expandedRowOffset: expandableConfig.expandedRowOffset,
                onTriggerExpand: onTriggerExpand,
                expandIconColumnIndex: expandableConfig.expandIconColumnIndex,
                indentSize: expandableConfig.indentSize,
                allColumnsFixedLeft: flattenColumns.every({
                    "Table.useMemo[TableContextValue]": function(col) {
                        return col.fixed === 'left';
                    }
                }["Table.useMemo[TableContextValue]"]),
                emptyNode: emptyNode,
                // Column
                columns: columns,
                flattenColumns: flattenColumns,
                onColumnResize: onColumnResize,
                colWidths: colWidths,
                // Row
                hoverStartRow: startRow,
                hoverEndRow: endRow,
                onHover: onHover,
                rowExpandable: expandableConfig.rowExpandable,
                onRow: onRow,
                getRowKey: getRowKey,
                expandedKeys: mergedExpandedKeys,
                childrenColumnName: mergedChildrenColumnName,
                rowHoverable: rowHoverable
            };
        }
    }["Table.useMemo[TableContextValue]"], [
        // Scroll
        mergedScrollX,
        // Table
        prefixCls,
        getComponent,
        scrollbarSize,
        direction,
        fixedInfoList,
        isSticky,
        supportSticky,
        componentWidth,
        fixHeader,
        fixColumn,
        horizonScroll,
        // Body
        mergedTableLayout,
        rowClassName,
        expandableConfig.expandedRowClassName,
        mergedExpandIcon,
        expandableType,
        expandableConfig.expandRowByClick,
        expandableConfig.expandedRowRender,
        expandableConfig.expandedRowOffset,
        onTriggerExpand,
        expandableConfig.expandIconColumnIndex,
        expandableConfig.indentSize,
        emptyNode,
        // Column
        columns,
        flattenColumns,
        onColumnResize,
        colWidths,
        // Row
        startRow,
        endRow,
        onHover,
        expandableConfig.rowExpandable,
        onRow,
        getRowKey,
        mergedExpandedKeys,
        mergedChildrenColumnName,
        rowHoverable
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Provider, {
        value: TableContextValue
    }, fullTable);
}
var RefTable = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(Table);
if ("TURBOPACK compile-time truthy", 1) {
    RefTable.displayName = 'Table';
}
function genTable(shouldTriggerRender) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["makeImmutable"])(RefTable, shouldTriggerRender);
}
var ImmutableTable = genTable();
ImmutableTable.EXPAND_COLUMN = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EXPAND_COLUMN"];
ImmutableTable.INTERNAL_HOOKS = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INTERNAL_HOOKS"];
ImmutableTable.Column = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$sugar$2f$Column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
ImmutableTable.ColumnGroup = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$sugar$2f$ColumnGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
ImmutableTable.Summary = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FooterComponents"];
const __TURBOPACK__default__export__ = ImmutableTable;
}}),
"[project]/node_modules/rc-table/es/VirtualTable/context.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GridContext": (()=>GridContext),
    "StaticContext": (()=>StaticContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
;
var StaticContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
var GridContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
}}),
"[project]/node_modules/rc-table/es/VirtualTable/VirtualCell.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "getColumnWidth": (()=>getColumnWidth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$BodyRow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Body/BodyRow.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Cell/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/VirtualTable/context.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
function getColumnWidth(colIndex, colSpan, columnsOffset) {
    var mergedColSpan = colSpan || 1;
    return columnsOffset[colIndex + mergedColSpan] - (columnsOffset[colIndex] || 0);
}
function VirtualCell(props) {
    var rowInfo = props.rowInfo, column = props.column, colIndex = props.colIndex, indent = props.indent, index = props.index, component = props.component, renderIndex = props.renderIndex, record = props.record, style = props.style, className = props.className, inverse = props.inverse, getHeight = props.getHeight;
    var render = column.render, dataIndex = column.dataIndex, columnClassName = column.className, colWidth = column.width;
    var _useContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GridContext"], [
        'columnsOffset'
    ]), columnsOffset = _useContext.columnsOffset;
    // TODO: support `expandableRowOffset`
    var _getCellProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Body$2f$BodyRow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCellProps"])(rowInfo, column, colIndex, indent, index), key = _getCellProps.key, fixedInfo = _getCellProps.fixedInfo, appendCellNode = _getCellProps.appendCellNode, additionalCellProps = _getCellProps.additionalCellProps;
    var cellStyle = additionalCellProps.style, _additionalCellProps$ = additionalCellProps.colSpan, colSpan = _additionalCellProps$ === void 0 ? 1 : _additionalCellProps$, _additionalCellProps$2 = additionalCellProps.rowSpan, rowSpan = _additionalCellProps$2 === void 0 ? 1 : _additionalCellProps$2;
    // ========================= ColWidth =========================
    // column width
    var startColIndex = colIndex - 1;
    var concatColWidth = getColumnWidth(startColIndex, colSpan, columnsOffset);
    // margin offset
    var marginOffset = colSpan > 1 ? colWidth - concatColWidth : 0;
    // ========================== Style ===========================
    var mergedStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, cellStyle), style), {}, {
        flex: "0 0 ".concat(concatColWidth, "px"),
        width: "".concat(concatColWidth, "px"),
        marginRight: marginOffset,
        pointerEvents: 'auto'
    });
    // When `colSpan` or `rowSpan` is `0`, should skip render.
    var needHide = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "VirtualCell.useMemo[needHide]": function() {
            if (inverse) {
                return rowSpan <= 1;
            } else {
                return colSpan === 0 || rowSpan === 0 || rowSpan > 1;
            }
        }
    }["VirtualCell.useMemo[needHide]"], [
        rowSpan,
        colSpan,
        inverse
    ]);
    // 0 rowSpan or colSpan should not render
    if (needHide) {
        mergedStyle.visibility = 'hidden';
    } else if (inverse) {
        mergedStyle.height = getHeight === null || getHeight === void 0 ? void 0 : getHeight(rowSpan);
    }
    var mergedRender = needHide ? function() {
        return null;
    } : render;
    // ========================== Render ==========================
    var cellSpan = {};
    // Virtual should reset `colSpan` & `rowSpan`
    if (rowSpan === 0 || colSpan === 0) {
        cellSpan.rowSpan = 1;
        cellSpan.colSpan = 1;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(columnClassName, className),
        ellipsis: column.ellipsis,
        align: column.align,
        scope: column.rowScope,
        component: component,
        prefixCls: rowInfo.prefixCls,
        key: key,
        record: record,
        index: index,
        renderIndex: renderIndex,
        dataIndex: dataIndex,
        render: mergedRender,
        shouldCellUpdate: column.shouldCellUpdate
    }, fixedInfo, {
        appendNode: appendCellNode,
        additionalProps: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, additionalCellProps), {}, {
            style: mergedStyle
        }, cellSpan)
    }));
}
const __TURBOPACK__default__export__ = VirtualCell;
}}),
"[project]/node_modules/rc-table/es/VirtualTable/BodyLine.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/defineProperty.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Cell/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRowInfo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useRowInfo.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$VirtualCell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/VirtualTable/VirtualCell.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/VirtualTable/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$expandUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/expandUtil.js [app-client] (ecmascript)");
;
;
;
;
var _excluded = [
    "data",
    "index",
    "className",
    "rowKey",
    "style",
    "extra",
    "getHeight"
];
;
;
;
;
;
;
;
;
;
var BodyLine = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(function(props, ref) {
    var data = props.data, index = props.index, className = props.className, rowKey = props.rowKey, style = props.style, extra = props.extra, getHeight = props.getHeight, restProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, _excluded);
    var record = data.record, indent = data.indent, renderIndex = data.index;
    var _useContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], [
        'prefixCls',
        'flattenColumns',
        'fixColumn',
        'componentWidth',
        'scrollX'
    ]), scrollX = _useContext.scrollX, flattenColumns = _useContext.flattenColumns, prefixCls = _useContext.prefixCls, fixColumn = _useContext.fixColumn, componentWidth = _useContext.componentWidth;
    var _useContext2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StaticContext"], [
        'getComponent'
    ]), getComponent = _useContext2.getComponent;
    var rowInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useRowInfo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(record, rowKey, index, indent);
    var RowComponent = getComponent([
        'body',
        'row'
    ], 'div');
    var cellComponent = getComponent([
        'body',
        'cell'
    ], 'div');
    // ========================== Expand ==========================
    var rowSupportExpand = rowInfo.rowSupportExpand, expanded = rowInfo.expanded, rowProps = rowInfo.rowProps, expandedRowRender = rowInfo.expandedRowRender, expandedRowClassName = rowInfo.expandedRowClassName;
    var expandRowNode;
    if (rowSupportExpand && expanded) {
        var expandContent = expandedRowRender(record, index, indent + 1, expanded);
        var expandedClsName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$expandUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["computedExpandedClassName"])(expandedRowClassName, record, index, indent);
        var additionalProps = {};
        if (fixColumn) {
            additionalProps = {
                style: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, '--virtual-width', "".concat(componentWidth, "px"))
            };
        }
        var rowCellCls = "".concat(prefixCls, "-expanded-row-cell");
        expandRowNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(RowComponent, {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("".concat(prefixCls, "-expanded-row"), "".concat(prefixCls, "-expanded-row-level-").concat(indent + 1), expandedClsName)
        }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Cell$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            component: cellComponent,
            prefixCls: prefixCls,
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(rowCellCls, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, "".concat(rowCellCls, "-fixed"), fixColumn)),
            additionalProps: additionalProps
        }, expandContent));
    }
    // ========================== Render ==========================
    var rowStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, style), {}, {
        width: scrollX
    });
    if (extra) {
        rowStyle.position = 'absolute';
        rowStyle.pointerEvents = 'none';
    }
    var rowNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(RowComponent, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, rowProps, restProps, {
        "data-row-key": rowKey,
        ref: rowSupportExpand ? null : ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(className, "".concat(prefixCls, "-row"), rowProps === null || rowProps === void 0 ? void 0 : rowProps.className, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, "".concat(prefixCls, "-row-extra"), extra)),
        style: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, rowStyle), rowProps === null || rowProps === void 0 ? void 0 : rowProps.style)
    }), flattenColumns.map(function(column, colIndex) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$VirtualCell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            key: colIndex,
            component: cellComponent,
            rowInfo: rowInfo,
            column: column,
            colIndex: colIndex,
            indent: indent,
            index: index,
            renderIndex: renderIndex,
            record: record,
            inverse: extra,
            getHeight: getHeight
        });
    }));
    if (rowSupportExpand) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
            ref: ref
        }, rowNode, expandRowNode);
    }
    return rowNode;
});
var ResponseBodyLine = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["responseImmutable"])(BodyLine);
if ("TURBOPACK compile-time truthy", 1) {
    ResponseBodyLine.displayName = 'BodyLine';
}
const __TURBOPACK__default__export__ = ResponseBodyLine;
}}),
"[project]/node_modules/rc-table/es/VirtualTable/BodyGrid.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$virtual$2d$list$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-virtual-list/es/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useFlattenRecords$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/hooks/useFlattenRecords.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$BodyLine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/VirtualTable/BodyLine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/VirtualTable/context.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
var Grid = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(function(props, ref) {
    var data = props.data, onScroll = props.onScroll;
    var _useContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], [
        'flattenColumns',
        'onColumnResize',
        'getRowKey',
        'prefixCls',
        'expandedKeys',
        'childrenColumnName',
        'scrollX',
        'direction'
    ]), flattenColumns = _useContext.flattenColumns, onColumnResize = _useContext.onColumnResize, getRowKey = _useContext.getRowKey, expandedKeys = _useContext.expandedKeys, prefixCls = _useContext.prefixCls, childrenColumnName = _useContext.childrenColumnName, scrollX = _useContext.scrollX, direction = _useContext.direction;
    var _useContext2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StaticContext"]), sticky = _useContext2.sticky, scrollY = _useContext2.scrollY, listItemHeight = _useContext2.listItemHeight, getComponent = _useContext2.getComponent, onTablePropScroll = _useContext2.onScroll;
    // =========================== Ref ============================
    var listRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    // =========================== Data ===========================
    var flattenData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$hooks$2f$useFlattenRecords$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(data, childrenColumnName, expandedKeys, getRowKey);
    // ========================== Column ==========================
    var columnsWidth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Grid.useMemo[columnsWidth]": function() {
            var total = 0;
            return flattenColumns.map({
                "Grid.useMemo[columnsWidth]": function(_ref) {
                    var width = _ref.width, key = _ref.key;
                    total += width;
                    return [
                        key,
                        width,
                        total
                    ];
                }
            }["Grid.useMemo[columnsWidth]"]);
        }
    }["Grid.useMemo[columnsWidth]"], [
        flattenColumns
    ]);
    var columnsOffset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Grid.useMemo[columnsOffset]": function() {
            return columnsWidth.map({
                "Grid.useMemo[columnsOffset]": function(colWidth) {
                    return colWidth[2];
                }
            }["Grid.useMemo[columnsOffset]"]);
        }
    }["Grid.useMemo[columnsOffset]"], [
        columnsWidth
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Grid.useEffect": function() {
            columnsWidth.forEach({
                "Grid.useEffect": function(_ref2) {
                    var _ref3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref2, 2), key = _ref3[0], width = _ref3[1];
                    onColumnResize(key, width);
                }
            }["Grid.useEffect"]);
        }
    }["Grid.useEffect"], [
        columnsWidth
    ]);
    // =========================== Ref ============================
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "Grid.useImperativeHandle": function() {
            var _listRef$current2;
            var obj = {
                scrollTo: function scrollTo(config) {
                    var _listRef$current;
                    (_listRef$current = listRef.current) === null || _listRef$current === void 0 || _listRef$current.scrollTo(config);
                },
                nativeElement: (_listRef$current2 = listRef.current) === null || _listRef$current2 === void 0 ? void 0 : _listRef$current2.nativeElement
            };
            Object.defineProperty(obj, 'scrollLeft', {
                get: function get() {
                    var _listRef$current3;
                    return ((_listRef$current3 = listRef.current) === null || _listRef$current3 === void 0 ? void 0 : _listRef$current3.getScrollInfo().x) || 0;
                },
                set: function set(value) {
                    var _listRef$current4;
                    (_listRef$current4 = listRef.current) === null || _listRef$current4 === void 0 || _listRef$current4.scrollTo({
                        left: value
                    });
                }
            });
            return obj;
        }
    }["Grid.useImperativeHandle"]);
    // ======================= Col/Row Span =======================
    var getRowSpan = function getRowSpan(column, index) {
        var _flattenData$index;
        var record = (_flattenData$index = flattenData[index]) === null || _flattenData$index === void 0 ? void 0 : _flattenData$index.record;
        var onCell = column.onCell;
        if (onCell) {
            var _cellProps$rowSpan;
            var cellProps = onCell(record, index);
            return (_cellProps$rowSpan = cellProps === null || cellProps === void 0 ? void 0 : cellProps.rowSpan) !== null && _cellProps$rowSpan !== void 0 ? _cellProps$rowSpan : 1;
        }
        return 1;
    };
    var extraRender = function extraRender(info) {
        var start = info.start, end = info.end, getSize = info.getSize, offsetY = info.offsetY;
        // Do nothing if no data
        if (end < 0) {
            return null;
        }
        // Find first rowSpan column
        var firstRowSpanColumns = flattenColumns.filter(// rowSpan is 0
        function(column) {
            return getRowSpan(column, start) === 0;
        });
        var startIndex = start;
        var _loop = function _loop(i) {
            firstRowSpanColumns = firstRowSpanColumns.filter(function(column) {
                return getRowSpan(column, i) === 0;
            });
            if (!firstRowSpanColumns.length) {
                startIndex = i;
                return 1; // break
            }
        };
        for(var i = start; i >= 0; i -= 1){
            if (_loop(i)) break;
        }
        // Find last rowSpan column
        var lastRowSpanColumns = flattenColumns.filter(// rowSpan is not 1
        function(column) {
            return getRowSpan(column, end) !== 1;
        });
        var endIndex = end;
        var _loop2 = function _loop2(_i) {
            lastRowSpanColumns = lastRowSpanColumns.filter(function(column) {
                return getRowSpan(column, _i) !== 1;
            });
            if (!lastRowSpanColumns.length) {
                endIndex = Math.max(_i - 1, end);
                return 1; // break
            }
        };
        for(var _i = end; _i < flattenData.length; _i += 1){
            if (_loop2(_i)) break;
        }
        // Collect the line who has rowSpan
        var spanLines = [];
        var _loop3 = function _loop3(_i2) {
            var item = flattenData[_i2];
            // This code will never reach, just incase
            if (!item) {
                return 1; // continue
            }
            if (flattenColumns.some(function(column) {
                return getRowSpan(column, _i2) > 1;
            })) {
                spanLines.push(_i2);
            }
        };
        for(var _i2 = startIndex; _i2 <= endIndex; _i2 += 1){
            if (_loop3(_i2)) continue;
        }
        // Patch extra line on the page
        var nodes = spanLines.map(function(index) {
            var item = flattenData[index];
            var rowKey = getRowKey(item.record, index);
            var getHeight = function getHeight(rowSpan) {
                var endItemIndex = index + rowSpan - 1;
                var endItemKey = getRowKey(flattenData[endItemIndex].record, endItemIndex);
                var sizeInfo = getSize(rowKey, endItemKey);
                return sizeInfo.bottom - sizeInfo.top;
            };
            var sizeInfo = getSize(rowKey);
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$BodyLine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                key: index,
                data: item,
                rowKey: rowKey,
                index: index,
                style: {
                    top: -offsetY + sizeInfo.top
                },
                extra: true,
                getHeight: getHeight
            });
        });
        return nodes;
    };
    // ========================= Context ==========================
    var gridContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Grid.useMemo[gridContext]": function() {
            return {
                columnsOffset: columnsOffset
            };
        }
    }["Grid.useMemo[gridContext]"], [
        columnsOffset
    ]);
    // ========================== Render ==========================
    var tblPrefixCls = "".concat(prefixCls, "-tbody");
    // default 'div' in rc-virtual-list
    var wrapperComponent = getComponent([
        'body',
        'wrapper'
    ]);
    // ========================== Sticky Scroll Bar ==========================
    var horizontalScrollBarStyle = {};
    if (sticky) {
        horizontalScrollBarStyle.position = 'sticky';
        horizontalScrollBarStyle.bottom = 0;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(sticky) === 'object' && sticky.offsetScroll) {
            horizontalScrollBarStyle.bottom = sticky.offsetScroll;
        }
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GridContext"].Provider, {
        value: gridContext
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$virtual$2d$list$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        fullHeight: false,
        ref: listRef,
        prefixCls: "".concat(tblPrefixCls, "-virtual"),
        styles: {
            horizontalScrollBar: horizontalScrollBarStyle
        },
        className: tblPrefixCls,
        height: scrollY,
        itemHeight: listItemHeight || 24,
        data: flattenData,
        itemKey: function itemKey(item) {
            return getRowKey(item.record);
        },
        component: wrapperComponent,
        scrollWidth: scrollX,
        direction: direction,
        onVirtualScroll: function onVirtualScroll(_ref4) {
            var _listRef$current5;
            var x = _ref4.x;
            onScroll({
                currentTarget: (_listRef$current5 = listRef.current) === null || _listRef$current5 === void 0 ? void 0 : _listRef$current5.nativeElement,
                scrollLeft: x
            });
        },
        onScroll: onTablePropScroll,
        extraRender: extraRender
    }, function(item, index, itemProps) {
        var rowKey = getRowKey(item.record, index);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$BodyLine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            data: item,
            rowKey: rowKey,
            index: index,
            style: itemProps.style
        });
    }));
});
var ResponseGrid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["responseImmutable"])(Grid);
if ("TURBOPACK compile-time truthy", 1) {
    ResponseGrid.displayName = 'ResponseGrid';
}
const __TURBOPACK__default__export__ = ResponseGrid;
}}),
"[project]/node_modules/rc-table/es/VirtualTable/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "genVirtualTable": (()=>genVirtualTable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useEvent.js [app-client] (ecmascript) <export default as useEvent>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__warning$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/warning.js [app-client] (ecmascript) <export default as warning>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/constant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/context/TableContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Table$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Table.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$BodyGrid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/VirtualTable/BodyGrid.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/VirtualTable/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$utils$2f$get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/utils/get.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
var renderBody = function renderBody(rawData, props) {
    var ref = props.ref, onScroll = props.onScroll;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$BodyGrid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ref: ref,
        data: rawData,
        onScroll: onScroll
    });
};
function VirtualTable(props, ref) {
    var data = props.data, columns = props.columns, scroll = props.scroll, sticky = props.sticky, _props$prefixCls = props.prefixCls, prefixCls = _props$prefixCls === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Table$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_PREFIX"] : _props$prefixCls, className = props.className, listItemHeight = props.listItemHeight, components = props.components, onScroll = props.onScroll;
    var _ref = scroll || {}, scrollX = _ref.x, scrollY = _ref.y;
    // Fill scrollX
    if (typeof scrollX !== 'number') {
        if ("TURBOPACK compile-time truthy", 1) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__warning$3e$__["warning"])(!scrollX, '`scroll.x` in virtual table must be number.');
        }
        scrollX = 1;
    }
    // Fill scrollY
    if (typeof scrollY !== 'number') {
        scrollY = 500;
        if ("TURBOPACK compile-time truthy", 1) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__warning$3e$__["warning"])(false, '`scroll.y` in virtual table must be number.');
        }
    }
    var getComponent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__["useEvent"])({
        "VirtualTable.useEvent[getComponent]": function(path, defaultComponent) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$utils$2f$get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(components, path) || defaultComponent;
        }
    }["VirtualTable.useEvent[getComponent]"]);
    // Memo this
    var onInternalScroll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__["useEvent"])(onScroll);
    // ========================= Context ==========================
    var context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "VirtualTable.useMemo[context]": function() {
            return {
                sticky: sticky,
                scrollY: scrollY,
                listItemHeight: listItemHeight,
                getComponent: getComponent,
                onScroll: onInternalScroll
            };
        }
    }["VirtualTable.useMemo[context]"], [
        sticky,
        scrollY,
        listItemHeight,
        getComponent,
        onInternalScroll
    ]);
    // ========================== Render ==========================
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StaticContext"].Provider, {
        value: context
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Table$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, props, {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(className, "".concat(prefixCls, "-virtual")),
        scroll: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, scroll), {}, {
            x: scrollX
        }),
        components: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, components), {}, {
            // fix https://github.com/ant-design/ant-design/issues/48991
            body: data !== null && data !== void 0 && data.length ? renderBody : undefined
        }),
        columns: columns,
        internalHooks: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INTERNAL_HOOKS"],
        tailor: true,
        ref: ref
    })));
}
var RefVirtualTable = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(VirtualTable);
if ("TURBOPACK compile-time truthy", 1) {
    RefVirtualTable.displayName = 'VirtualTable';
}
function genVirtualTable(shouldTriggerRender) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$context$2f$TableContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["makeImmutable"])(RefVirtualTable, shouldTriggerRender);
}
const __TURBOPACK__default__export__ = genVirtualTable();
}}),
"[project]/node_modules/rc-table/es/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/constant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Footer/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$sugar$2f$Column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/sugar/Column.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$sugar$2f$ColumnGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/sugar/ColumnGroup.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Table$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Table.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/legacyUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/VirtualTable/index.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Table$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
}}),
"[project]/node_modules/rc-table/es/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/constant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Footer/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$sugar$2f$Column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/sugar/Column.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$sugar$2f$ColumnGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/sugar/ColumnGroup.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Table$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Table.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/utils/legacyUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$VirtualTable$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/VirtualTable/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/index.js [app-client] (ecmascript) <locals>");
}}),
"[project]/node_modules/rc-table/es/Footer/index.js [app-client] (ecmascript) <export FooterComponents as Summary>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Summary": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FooterComponents"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$table$2f$es$2f$Footer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-table/es/Footer/index.js [app-client] (ecmascript)");
}}),
}]);

//# sourceMappingURL=node_modules_rc-table_es_23647edc._.js.map