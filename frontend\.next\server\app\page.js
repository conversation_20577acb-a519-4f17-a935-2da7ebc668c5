/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUMlRTklQTElQjklRTclOUIlQUUlRTYlOTYlODclRTQlQkIlQjYlRTUlQTQlQjklNUMlNUNYQVAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBaUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXOmhueebruaWh+S7tuWkuVxcXFxYQVBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DConfigProvider%3AD%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cantd%5C%5Ces%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22ConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40ant-design%5C%5Cnextjs-registry%5C%5Ces%5C%5CAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DConfigProvider%3AD%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cantd%5C%5Ces%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22ConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40ant-design%5C%5Cnextjs-registry%5C%5Ces%5C%5CAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js */ \"(rsc)/__barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js */ \"(rsc)/./node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DConfigProvider%3AD%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cantd%5C%5Ces%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22ConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40ant-design%5C%5Cnextjs-registry%5C%5Ces%5C%5CAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm67mlofku7blpLlcXFhBUFxcZnJvbnRlbmRcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxc6aG555uu5paH5Lu25aS5XFxYQVBcXGZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MTljYjBmYzNmNjNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ant-design/nextjs-registry */ \"(rsc)/./node_modules/@ant-design/nextjs-registry/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ConfigProvider!=!antd */ \"(rsc)/__barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(rsc)/./node_modules/antd/lib/locale/zh_CN.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"AI学习和咨询系统\",\n    description: \"专为监狱服刑人员设计的AI学习和咨询系统\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_2__.AntdRegistry, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_5__.ConfigProvider, {\n                    locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\项目文件夹\\XAP\\frontend\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\项目文件夹\\XAP\\frontend\\src\\contexts\\AuthContext.tsx",
"useAuth",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\项目文件夹\\XAP\\frontend\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(rsc)/__barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js":
/*!**********************************************************************************!*\
  !*** __barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConfigProvider: () => (/* binding */ ConfigProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ConfigProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ConfigProvider() from the server but ConfigProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\项目文件夹\\XAP\\frontend\\node_modules\\antd\\es\\index.js@__barrel_optimize__?names=ConfigProvider",
"ConfigProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUMlRTklQTElQjklRTclOUIlQUUlRTYlOTYlODclRTQlQkIlQjYlRTUlQTQlQjklNUMlNUNYQVAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBaUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXOmhueebruaWh+S7tuWkuVxcXFxYQVBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DConfigProvider%3AD%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cantd%5C%5Ces%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22ConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40ant-design%5C%5Cnextjs-registry%5C%5Ces%5C%5CAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DConfigProvider%3AD%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cantd%5C%5Ces%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22ConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40ant-design%5C%5Cnextjs-registry%5C%5Ces%5C%5CAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js */ \"(ssr)/__barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js */ \"(ssr)/./node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMl9fYmFycmVsX29wdGltaXplX18lM0ZuYW1lcyUzRENvbmZpZ1Byb3ZpZGVyJTNBRCUzQSU1QyU1QyVFOSVBMSVCOSVFNyU5QiVBRSVFNiU5NiU4NyVFNCVCQiVCNiVFNSVBNCVCOSU1QyU1Q1hBUCU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDYW50ZCU1QyU1Q2VzJTVDJTVDaW5kZXguanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDb25maWdQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDJUU5JUExJUI5JUU3JTlCJUFFJUU2JTk2JTg3JUU0JUJCJUI2JUU1JUE0JUI5JTVDJTVDWEFQJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMlNDBhbnQtZGVzaWduJTVDJTVDbmV4dGpzLXJlZ2lzdHJ5JTVDJTVDZXMlNUMlNUNBbnRkUmVnaXN0cnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUMlRTklQTElQjklRTclOUIlQUUlRTYlOTYlODclRTQlQkIlQjYlRTUlQTQlQjklNUMlNUNYQVAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDJUU5JUExJUI5JUU3JTlCJUFFJUU2JTk2JTg3JUU0JUJCJUI2JUU1JUE0JUI5JTVDJTVDWEFQJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDJUU5JUExJUI5JUU3JTlCJUFFJUU2JTk2JTg3JUU0JUJCJUI2JUU1JUE0JUI5JTVDJTVDWEFQJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNjb250ZXh0cyU1QyU1Q0F1dGhDb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ1FBQThLO0FBQzlLO0FBQ0Esc09BQTJKO0FBQzNKO0FBQ0Esd0tBQStIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDb25maWdQcm92aWRlclwiXSAqLyBcIl9fYmFycmVsX29wdGltaXplX18/bmFtZXM9Q29uZmlnUHJvdmlkZXIhPSFEOlxcXFzpobnnm67mlofku7blpLlcXFxcWEFQXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxhbnRkXFxcXGVzXFxcXGluZGV4LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxc6aG555uu5paH5Lu25aS5XFxcXFhBUFxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcQGFudC1kZXNpZ25cXFxcbmV4dGpzLXJlZ2lzdHJ5XFxcXGVzXFxcXEFudGRSZWdpc3RyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiRDpcXFxc6aG555uu5paH5Lu25aS5XFxcXFhBUFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DConfigProvider%3AD%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cantd%5C%5Ces%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22ConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40ant-design%5C%5Cnextjs-registry%5C%5Ces%5C%5CAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Spin!=!antd */ \"(ssr)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const { user, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (!loading) {\n                if (!user) {\n                    router.push('/login');\n                } else if (user.role === 'admin') {\n                    router.push('/admin');\n                } else {\n                    router.push('/chat');\n                }\n            }\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 检查本地存储的用户信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": async ()=>{\n                    try {\n                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get('token');\n                        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get('user');\n                        if (token && savedUser) {\n                            const userData = JSON.parse(savedUser);\n                            setUser(userData);\n                            // 验证token是否仍然有效\n                            try {\n                                await refreshUser();\n                            } catch (error) {\n                                // Token无效，清除本地存储\n                                logout();\n                            }\n                        }\n                    } catch (error) {\n                        console.error('初始化认证状态失败:', error);\n                        logout();\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (userId, password)=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.userAPI.login(userId, password);\n            if (response.token && response.user) {\n                // 保存token和用户信息\n                js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set('token', response.token, {\n                    expires: 1\n                }); // 1天过期\n                js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set('user', JSON.stringify(response.user), {\n                    expires: 1\n                });\n                setUser(response.user);\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success('登录成功');\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error('登录失败:', error);\n            const errorMessage = error.response?.data?.message || '登录失败，请检查用户ID和密码';\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(errorMessage);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove('token');\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove('user');\n        setUser(null);\n        _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].info('已退出登录');\n    };\n    const refreshUser = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.userAPI.getProfile();\n            if (response.user) {\n                setUser(response.user);\n                js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set('user', JSON.stringify(response.user), {\n                    expires: 1\n                });\n            }\n        } catch (error) {\n            console.error('刷新用户信息失败:', error);\n            throw error;\n        }\n    };\n    const isAdmin = user?.role === 'admin';\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        refreshUser,\n        isAdmin\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvQXV0aENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFeUY7QUFDekQ7QUFDSTtBQUNMO0FBcUIvQixNQUFNUSw0QkFBY1Asb0RBQWFBLENBQThCUTtBQUV4RCxNQUFNQyxVQUFVO0lBQ3JCLE1BQU1DLFVBQVVULGlEQUFVQSxDQUFDTTtJQUMzQixJQUFJRyxZQUFZRixXQUFXO1FBQ3pCLE1BQU0sSUFBSUcsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBRTtBQU1LLE1BQU1FLGVBQTRDLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQ3BFLE1BQU0sQ0FBQ0MsTUFBTUMsUUFBUSxHQUFHYiwrQ0FBUUEsQ0FBYztJQUM5QyxNQUFNLENBQUNjLFNBQVNDLFdBQVcsR0FBR2YsK0NBQVFBLENBQUM7SUFFdkMsY0FBYztJQUNkQyxnREFBU0E7a0NBQUM7WUFDUixNQUFNZTttREFBVztvQkFDZixJQUFJO3dCQUNGLE1BQU1DLFFBQVFmLGlEQUFPQSxDQUFDZ0IsR0FBRyxDQUFDO3dCQUMxQixNQUFNQyxZQUFZakIsaURBQU9BLENBQUNnQixHQUFHLENBQUM7d0JBRTlCLElBQUlELFNBQVNFLFdBQVc7NEJBQ3RCLE1BQU1DLFdBQVdDLEtBQUtDLEtBQUssQ0FBQ0g7NEJBQzVCTixRQUFRTzs0QkFFUixnQkFBZ0I7NEJBQ2hCLElBQUk7Z0NBQ0YsTUFBTUc7NEJBQ1IsRUFBRSxPQUFPQyxPQUFPO2dDQUNkLGlCQUFpQjtnQ0FDakJDOzRCQUNGO3dCQUNGO29CQUNGLEVBQUUsT0FBT0QsT0FBTzt3QkFDZEUsUUFBUUYsS0FBSyxDQUFDLGNBQWNBO3dCQUM1QkM7b0JBQ0YsU0FBVTt3QkFDUlYsV0FBVztvQkFDYjtnQkFDRjs7WUFFQUM7UUFDRjtpQ0FBRyxFQUFFO0lBRUwsTUFBTVcsUUFBUSxPQUFPQyxRQUFnQkM7UUFDbkMsSUFBSTtZQUNGZCxXQUFXO1lBQ1gsTUFBTWUsV0FBVyxNQUFNM0IsNkNBQU9BLENBQUN3QixLQUFLLENBQUNDLFFBQVFDO1lBRTdDLElBQUlDLFNBQVNiLEtBQUssSUFBSWEsU0FBU2xCLElBQUksRUFBRTtnQkFDbkMsZUFBZTtnQkFDZlYsaURBQU9BLENBQUM2QixHQUFHLENBQUMsU0FBU0QsU0FBU2IsS0FBSyxFQUFFO29CQUFFZSxTQUFTO2dCQUFFLElBQUksT0FBTztnQkFDN0Q5QixpREFBT0EsQ0FBQzZCLEdBQUcsQ0FBQyxRQUFRVixLQUFLWSxTQUFTLENBQUNILFNBQVNsQixJQUFJLEdBQUc7b0JBQUVvQixTQUFTO2dCQUFFO2dCQUVoRW5CLFFBQVFpQixTQUFTbEIsSUFBSTtnQkFDckJSLDJFQUFPQSxDQUFDOEIsT0FBTyxDQUFDO2dCQUNoQixPQUFPO1lBQ1Q7WUFFQSxPQUFPO1FBQ1QsRUFBRSxPQUFPVixPQUFZO1lBQ25CRSxRQUFRRixLQUFLLENBQUMsU0FBU0E7WUFDdkIsTUFBTVcsZUFBZVgsTUFBTU0sUUFBUSxFQUFFTSxNQUFNaEMsV0FBVztZQUN0REEsMkVBQU9BLENBQUNvQixLQUFLLENBQUNXO1lBQ2QsT0FBTztRQUNULFNBQVU7WUFDUnBCLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTVUsU0FBUztRQUNidkIsaURBQU9BLENBQUNtQyxNQUFNLENBQUM7UUFDZm5DLGlEQUFPQSxDQUFDbUMsTUFBTSxDQUFDO1FBQ2Z4QixRQUFRO1FBQ1JULDJFQUFPQSxDQUFDa0MsSUFBSSxDQUFDO0lBQ2Y7SUFFQSxNQUFNZixjQUFjO1FBQ2xCLElBQUk7WUFDRixNQUFNTyxXQUFXLE1BQU0zQiw2Q0FBT0EsQ0FBQ29DLFVBQVU7WUFDekMsSUFBSVQsU0FBU2xCLElBQUksRUFBRTtnQkFDakJDLFFBQVFpQixTQUFTbEIsSUFBSTtnQkFDckJWLGlEQUFPQSxDQUFDNkIsR0FBRyxDQUFDLFFBQVFWLEtBQUtZLFNBQVMsQ0FBQ0gsU0FBU2xCLElBQUksR0FBRztvQkFBRW9CLFNBQVM7Z0JBQUU7WUFDbEU7UUFDRixFQUFFLE9BQU9SLE9BQU87WUFDZEUsUUFBUUYsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLE1BQU1nQixVQUFVNUIsTUFBTTZCLFNBQVM7SUFFL0IsTUFBTUMsUUFBeUI7UUFDN0I5QjtRQUNBRTtRQUNBYTtRQUNBRjtRQUNBRjtRQUNBaUI7SUFDRjtJQUVBLHFCQUNFLDhEQUFDbkMsWUFBWXNDLFFBQVE7UUFBQ0QsT0FBT0E7a0JBQzFCL0I7Ozs7OztBQUdQLEVBQUUiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm67mlofku7blpLlcXFhBUFxcZnJvbnRlbmRcXHNyY1xcY29udGV4dHNcXEF1dGhDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgQ29va2llcyBmcm9tICdqcy1jb29raWUnO1xuaW1wb3J0IHsgdXNlckFQSSB9IGZyb20gJ0AvbGliL2FwaSc7XG5pbXBvcnQgeyBtZXNzYWdlIH0gZnJvbSAnYW50ZCc7XG5cbmludGVyZmFjZSBVc2VyIHtcbiAgaWQ6IHN0cmluZztcbiAgdXNlcm5hbWU6IHN0cmluZztcbiAgcm9sZTogJ3VzZXInIHwgJ2FkbWluJztcbiAgdG9rZW5zOiBudW1iZXI7XG4gIHN0YXR1czogc3RyaW5nO1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gIGxhc3RfbG9naW4/OiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUge1xuICB1c2VyOiBVc2VyIHwgbnVsbDtcbiAgbG9hZGluZzogYm9vbGVhbjtcbiAgbG9naW46ICh1c2VySWQ6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykgPT4gUHJvbWlzZTxib29sZWFuPjtcbiAgbG9nb3V0OiAoKSA9PiB2b2lkO1xuICByZWZyZXNoVXNlcjogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgaXNBZG1pbjogYm9vbGVhbjtcbn1cblxuY29uc3QgQXV0aENvbnRleHQgPSBjcmVhdGVDb250ZXh0PEF1dGhDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTtcblxuZXhwb3J0IGNvbnN0IHVzZUF1dGggPSAoKSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufTtcblxuaW50ZXJmYWNlIEF1dGhQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGNvbnN0IEF1dGhQcm92aWRlcjogUmVhY3QuRkM8QXV0aFByb3ZpZGVyUHJvcHM+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxVc2VyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuXG4gIC8vIOajgOafpeacrOWcsOWtmOWCqOeahOeUqOaIt+S/oeaBr1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGluaXRBdXRoID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBDb29raWVzLmdldCgndG9rZW4nKTtcbiAgICAgICAgY29uc3Qgc2F2ZWRVc2VyID0gQ29va2llcy5nZXQoJ3VzZXInKTtcblxuICAgICAgICBpZiAodG9rZW4gJiYgc2F2ZWRVc2VyKSB7XG4gICAgICAgICAgY29uc3QgdXNlckRhdGEgPSBKU09OLnBhcnNlKHNhdmVkVXNlcik7XG4gICAgICAgICAgc2V0VXNlcih1c2VyRGF0YSk7XG4gICAgICAgICAgXG4gICAgICAgICAgLy8g6aqM6K+BdG9rZW7mmK/lkKbku43nhLbmnInmlYhcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgYXdhaXQgcmVmcmVzaFVzZXIoKTtcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgLy8gVG9rZW7ml6DmlYjvvIzmuIXpmaTmnKzlnLDlrZjlgqhcbiAgICAgICAgICAgIGxvZ291dCgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign5Yid5aeL5YyW6K6k6K+B54q25oCB5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgbG9nb3V0KCk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgaW5pdEF1dGgoKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGxvZ2luID0gYXN5bmMgKHVzZXJJZDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHVzZXJBUEkubG9naW4odXNlcklkLCBwYXNzd29yZCk7XG4gICAgICBcbiAgICAgIGlmIChyZXNwb25zZS50b2tlbiAmJiByZXNwb25zZS51c2VyKSB7XG4gICAgICAgIC8vIOS/neWtmHRva2Vu5ZKM55So5oi35L+h5oGvXG4gICAgICAgIENvb2tpZXMuc2V0KCd0b2tlbicsIHJlc3BvbnNlLnRva2VuLCB7IGV4cGlyZXM6IDEgfSk7IC8vIDHlpKnov4fmnJ9cbiAgICAgICAgQ29va2llcy5zZXQoJ3VzZXInLCBKU09OLnN0cmluZ2lmeShyZXNwb25zZS51c2VyKSwgeyBleHBpcmVzOiAxIH0pO1xuICAgICAgICBcbiAgICAgICAgc2V0VXNlcihyZXNwb25zZS51c2VyKTtcbiAgICAgICAgbWVzc2FnZS5zdWNjZXNzKCfnmbvlvZXmiJDlip8nKTtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICB9XG4gICAgICBcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfnmbvlvZXlpLHotKU6JywgZXJyb3IpO1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ+eZu+W9leWksei0pe+8jOivt+ajgOafpeeUqOaIt0lE5ZKM5a+G56CBJztcbiAgICAgIG1lc3NhZ2UuZXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvZ291dCA9ICgpID0+IHtcbiAgICBDb29raWVzLnJlbW92ZSgndG9rZW4nKTtcbiAgICBDb29raWVzLnJlbW92ZSgndXNlcicpO1xuICAgIHNldFVzZXIobnVsbCk7XG4gICAgbWVzc2FnZS5pbmZvKCflt7LpgIDlh7rnmbvlvZUnKTtcbiAgfTtcblxuICBjb25zdCByZWZyZXNoVXNlciA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1c2VyQVBJLmdldFByb2ZpbGUoKTtcbiAgICAgIGlmIChyZXNwb25zZS51c2VyKSB7XG4gICAgICAgIHNldFVzZXIocmVzcG9uc2UudXNlcik7XG4gICAgICAgIENvb2tpZXMuc2V0KCd1c2VyJywgSlNPTi5zdHJpbmdpZnkocmVzcG9uc2UudXNlciksIHsgZXhwaXJlczogMSB9KTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign5Yi35paw55So5oi35L+h5oGv5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBpc0FkbWluID0gdXNlcj8ucm9sZSA9PT0gJ2FkbWluJztcblxuICBjb25zdCB2YWx1ZTogQXV0aENvbnRleHRUeXBlID0ge1xuICAgIHVzZXIsXG4gICAgbG9hZGluZyxcbiAgICBsb2dpbixcbiAgICBsb2dvdXQsXG4gICAgcmVmcmVzaFVzZXIsXG4gICAgaXNBZG1pbixcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxBdXRoQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvQXV0aENvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNvb2tpZXMiLCJ1c2VyQVBJIiwibWVzc2FnZSIsIkF1dGhDb250ZXh0IiwidW5kZWZpbmVkIiwidXNlQXV0aCIsImNvbnRleHQiLCJFcnJvciIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwidXNlciIsInNldFVzZXIiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImluaXRBdXRoIiwidG9rZW4iLCJnZXQiLCJzYXZlZFVzZXIiLCJ1c2VyRGF0YSIsIkpTT04iLCJwYXJzZSIsInJlZnJlc2hVc2VyIiwiZXJyb3IiLCJsb2dvdXQiLCJjb25zb2xlIiwibG9naW4iLCJ1c2VySWQiLCJwYXNzd29yZCIsInJlc3BvbnNlIiwic2V0IiwiZXhwaXJlcyIsInN0cmluZ2lmeSIsInN1Y2Nlc3MiLCJlcnJvck1lc3NhZ2UiLCJkYXRhIiwicmVtb3ZlIiwiaW5mbyIsImdldFByb2ZpbGUiLCJpc0FkbWluIiwicm9sZSIsInZhbHVlIiwiUHJvdmlkZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conversationAPI: () => (/* binding */ conversationAPI),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   messageAPI: () => (/* binding */ messageAPI),\n/* harmony export */   systemAPI: () => (/* binding */ systemAPI),\n/* harmony export */   userAPI: () => (/* binding */ userAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n\nconst API_BASE_URL = \"http://localhost:3001/api\" || 0;\n// 创建axios实例\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 30000,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// 请求拦截器 - 添加认证token\napi.interceptors.request.use((config)=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('token');\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器 - 处理错误\napi.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response?.status === 401) {\n        // Token过期或无效，清除本地存储并跳转到登录页\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove('token');\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove('user');\n        if (false) {}\n    }\n    return Promise.reject(error);\n});\n// 用户相关API\nconst userAPI = {\n    // 登录\n    login: async (userId, password)=>{\n        const response = await api.post('/users/login', {\n            userId,\n            password\n        });\n        return response.data;\n    },\n    // 获取用户信息\n    getProfile: async ()=>{\n        const response = await api.get('/users/profile');\n        return response.data;\n    },\n    // 创建用户（管理员）\n    createUser: async (userData)=>{\n        const response = await api.post('/users', userData);\n        return response.data;\n    },\n    // 获取所有用户（管理员）\n    getAllUsers: async (page = 1, limit = 20)=>{\n        const response = await api.get(`/users?page=${page}&limit=${limit}`);\n        return response.data;\n    },\n    // 充值令牌（管理员）\n    rechargeTokens: async (userId, amount, description)=>{\n        const response = await api.post(`/users/${userId}/recharge`, {\n            amount,\n            description\n        });\n        return response.data;\n    },\n    // 更新用户状态（管理员）\n    updateUserStatus: async (userId, status)=>{\n        const response = await api.patch(`/users/${userId}/status`, {\n            status\n        });\n        return response.data;\n    },\n    // 获取用户令牌使用记录（管理员）\n    getTokenUsage: async (userId, page = 1, limit = 20, filters)=>{\n        let url = `/users/${userId}/token-usage?page=${page}&limit=${limit}`;\n        if (filters?.actionType && filters.actionType !== 'all') {\n            url += `&actionType=${filters.actionType}`;\n        }\n        if (filters?.startDate) {\n            url += `&startDate=${filters.startDate}`;\n        }\n        if (filters?.endDate) {\n            url += `&endDate=${filters.endDate}`;\n        }\n        const response = await api.get(url);\n        return response.data;\n    }\n};\n// 对话相关API\nconst conversationAPI = {\n    // 创建对话\n    create: async (title)=>{\n        const response = await api.post('/conversations', {\n            title\n        });\n        return response.data;\n    },\n    // 获取用户对话列表\n    getUserConversations: async (status = 'active')=>{\n        const response = await api.get(`/conversations/my?status=${status}`);\n        return response.data;\n    },\n    // 获取对话详情\n    getConversation: async (conversationId, page = 1, limit = 50)=>{\n        const response = await api.get(`/conversations/${conversationId}?page=${page}&limit=${limit}`);\n        return response.data;\n    },\n    // 更新对话标题\n    updateTitle: async (conversationId, title)=>{\n        const response = await api.patch(`/conversations/${conversationId}/title`, {\n            title\n        });\n        return response.data;\n    },\n    // 删除对话\n    delete: async (conversationId)=>{\n        const response = await api.delete(`/conversations/${conversationId}`);\n        return response.data;\n    },\n    // 获取所有对话（管理员）\n    getAllConversations: async (page = 1, limit = 20, filters)=>{\n        const params = new URLSearchParams({\n            page: page.toString(),\n            limit: limit.toString()\n        });\n        if (filters?.userId) params.append('userId', filters.userId);\n        if (filters?.status) params.append('status', filters.status);\n        const response = await api.get(`/conversations?${params.toString()}`);\n        return response.data;\n    }\n};\n// 消息相关API\nconst messageAPI = {\n    // 发送消息\n    sendMessage: async (conversationId, content)=>{\n        const response = await api.post('/messages', {\n            conversationId,\n            content\n        });\n        return response.data;\n    },\n    // 获取对话消息\n    getMessages: async (conversationId, page = 1, limit = 50)=>{\n        const response = await api.get(`/messages/${conversationId}?page=${page}&limit=${limit}`);\n        return response.data;\n    },\n    // 流式发送消息\n    sendMessageStream: async (conversationId, content, onChunk)=>{\n        const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('token');\n        const eventSource = new EventSource(`${API_BASE_URL}/messages/stream`, {\n            headers: {\n                'Authorization': `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            }\n        });\n        // 发送消息数据\n        await fetch(`${API_BASE_URL}/messages/stream`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                conversationId,\n                content\n            })\n        });\n        eventSource.onmessage = (event)=>{\n            try {\n                const data = JSON.parse(event.data);\n                onChunk(data);\n                if (data.type === 'completed' || data.type === 'error') {\n                    eventSource.close();\n                }\n            } catch (error) {\n                console.error('解析SSE数据失败:', error);\n            }\n        };\n        eventSource.onerror = (error)=>{\n            console.error('SSE连接错误:', error);\n            eventSource.close();\n        };\n        return eventSource;\n    }\n};\n// 系统相关API\nconst systemAPI = {\n    // 检查Ollama服务状态\n    checkOllamaHealth: async ()=>{\n        const response = await api.get('/system/ollama/health');\n        return response.data;\n    },\n    // 获取可用模型列表\n    getModels: async ()=>{\n        const response = await api.get('/system/ollama/models');\n        return response.data;\n    },\n    // 获取系统统计信息\n    getStats: async (startDate, endDate)=>{\n        const params = new URLSearchParams();\n        if (startDate) params.append('startDate', startDate);\n        if (endDate) params.append('endDate', endDate);\n        const response = await api.get(`/system/stats?${params.toString()}`);\n        return response.data;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js":
/*!**********************************************************************************!*\
  !*** __barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfigProvider: () => (/* reexport safe */ _config_provider__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config-provider */ \"(ssr)/./node_modules/antd/es/config-provider/index.js\");\n/* __next_internal_client_entry_do_not_use__ ConfigProvider auto */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Db25maWdQcm92aWRlciE9IS4vbm9kZV9tb2R1bGVzL2FudGQvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7b0VBRTZEIiwic291cmNlcyI6WyJEOlxc6aG555uu5paH5Lu25aS5XFxYQVBcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGFudGRcXGVzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb25maWdQcm92aWRlciB9IGZyb20gXCIuL2NvbmZpZy1wcm92aWRlclwiIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJDb25maWdQcm92aWRlciJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/antd","vendor-chunks/rc-picker","vendor-chunks/@ant-design","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/rc-util","vendor-chunks/rc-motion","vendor-chunks/rc-notification","vendor-chunks/rc-pagination","vendor-chunks/@babel","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/stylis","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/@emotion","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/classnames","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/throttle-debounce"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();