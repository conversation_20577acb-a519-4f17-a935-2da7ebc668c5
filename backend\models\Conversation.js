const { query } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

class Conversation {
  // 根据ID查找对话
  static async findById(id) {
    const sql = 'SELECT * FROM conversations WHERE id = ?';
    const conversations = await query(sql, [id]);
    return conversations[0] || null;
  }

  // 创建新对话
  static async create(userId, title) {
    // 检查用户对话数量限制
    const userConversations = await this.getByUserId(userId);
    if (userConversations.length >= 10) {
      throw new Error('每个用户最多只能有10个对话');
    }

    const id = uuidv4();
    const sql = `
      INSERT INTO conversations (id, user_id, title)
      VALUES (?, ?, ?)
    `;
    
    await query(sql, [id, userId, title]);
    return await this.findById(id);
  }

  // 获取用户的所有对话
  static async getByUserId(userId, status = 'active') {
    const sql = `
      SELECT c.*, 
             (SELECT COUNT(*) FROM messages WHERE conversation_id = c.id) as message_count,
             (SELECT created_at FROM messages WHERE conversation_id = c.id ORDER BY created_at DESC LIMIT 1) as last_message_time
      FROM conversations c
      WHERE c.user_id = ? AND c.status = ?
      ORDER BY c.updated_at DESC
    `;
    
    return await query(sql, [userId, status]);
  }

  // 更新对话标题
  static async updateTitle(id, title) {
    const sql = 'UPDATE conversations SET title = ? WHERE id = ?';
    await query(sql, [title, id]);
    return await this.findById(id);
  }

  // 更新对话状态
  static async updateStatus(id, status) {
    const sql = 'UPDATE conversations SET status = ? WHERE id = ?';
    await query(sql, [status, id]);
    return await this.findById(id);
  }

  // 删除对话（软删除）
  static async delete(id) {
    return await this.updateStatus(id, 'deleted');
  }

  // 归档对话
  static async archive(id) {
    return await this.updateStatus(id, 'archived');
  }

  // 获取所有对话（管理员功能）
  static async getAll(page = 1, limit = 20, filters = {}) {
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE 1=1';
    const params = [];

    if (filters.userId) {
      whereClause += ' AND c.user_id = ?';
      params.push(filters.userId);
    }

    if (filters.status) {
      whereClause += ' AND c.status = ?';
      params.push(filters.status);
    }

    const countSql = `SELECT COUNT(*) as total FROM conversations c ${whereClause}`;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;

    const sql = `
      SELECT c.*, u.username,
             (SELECT COUNT(*) FROM messages WHERE conversation_id = c.id) as message_count
      FROM conversations c
      LEFT JOIN users u ON c.user_id = u.id
      ${whereClause}
      ORDER BY c.updated_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const conversations = await query(sql, params);

    return {
      conversations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  // 检查对话是否属于用户
  static async belongsToUser(conversationId, userId) {
    const conversation = await this.findById(conversationId);
    return conversation && conversation.user_id === userId;
  }

  // 更新对话的最后活动时间
  static async updateLastActivity(id) {
    const sql = 'UPDATE conversations SET updated_at = CURRENT_TIMESTAMP WHERE id = ?';
    await query(sql, [id]);
  }
}

module.exports = Conversation;
