import axios from 'axios';
import Cookies from 'js-cookie';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token过期或无效，清除本地存储并跳转到登录页
      Cookies.remove('token');
      Cookies.remove('user');
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// 用户相关API
export const userAPI = {
  // 登录
  login: async (userId: string, password: string) => {
    const response = await api.post('/users/login', { userId, password });
    return response.data;
  },

  // 获取用户信息
  getProfile: async () => {
    const response = await api.get('/users/profile');
    return response.data;
  },

  // 创建用户（管理员）
  createUser: async (userData: { username: string; password: string; tokens?: number }) => {
    const response = await api.post('/users', userData);
    return response.data;
  },

  // 获取所有用户（管理员）
  getAllUsers: async (page = 1, limit = 20) => {
    const response = await api.get(`/users?page=${page}&limit=${limit}`);
    return response.data;
  },

  // 充值令牌（管理员）
  rechargeTokens: async (userId: string, amount: number, description?: string) => {
    const response = await api.post(`/users/${userId}/recharge`, { amount, description });
    return response.data;
  },

  // 更新用户状态（管理员）
  updateUserStatus: async (userId: string, status: 'active' | 'inactive' | 'banned') => {
    const response = await api.patch(`/users/${userId}/status`, { status });
    return response.data;
  },

  // 获取用户令牌使用记录（管理员）
  getTokenUsage: async (userId: string, page = 1, limit = 20, filters?: {
    actionType?: string;
    startDate?: string;
    endDate?: string;
  }) => {
    let url = `/users/${userId}/token-usage?page=${page}&limit=${limit}`;
    if (filters?.actionType && filters.actionType !== 'all') {
      url += `&actionType=${filters.actionType}`;
    }
    if (filters?.startDate) {
      url += `&startDate=${filters.startDate}`;
    }
    if (filters?.endDate) {
      url += `&endDate=${filters.endDate}`;
    }
    const response = await api.get(url);
    return response.data;
  },
};

// 对话相关API
export const conversationAPI = {
  // 创建对话
  create: async (title: string) => {
    const response = await api.post('/conversations', { title });
    return response.data;
  },

  // 获取用户对话列表
  getUserConversations: async (status = 'active') => {
    const response = await api.get(`/conversations/my?status=${status}`);
    return response.data;
  },

  // 获取对话详情
  getConversation: async (conversationId: string, page = 1, limit = 50) => {
    const response = await api.get(`/conversations/${conversationId}?page=${page}&limit=${limit}`);
    return response.data;
  },

  // 更新对话标题
  updateTitle: async (conversationId: string, title: string) => {
    const response = await api.patch(`/conversations/${conversationId}/title`, { title });
    return response.data;
  },

  // 删除对话
  delete: async (conversationId: string) => {
    const response = await api.delete(`/conversations/${conversationId}`);
    return response.data;
  },

  // 获取所有对话（管理员）
  getAllConversations: async (page = 1, limit = 20, filters?: { userId?: string; status?: string }) => {
    const params = new URLSearchParams({ page: page.toString(), limit: limit.toString() });
    if (filters?.userId) params.append('userId', filters.userId);
    if (filters?.status) params.append('status', filters.status);
    
    const response = await api.get(`/conversations?${params.toString()}`);
    return response.data;
  },
};

// 消息相关API
export const messageAPI = {
  // 发送消息
  sendMessage: async (conversationId: string, content: string) => {
    const response = await api.post('/messages', { conversationId, content });
    return response.data;
  },

  // 获取对话消息
  getMessages: async (conversationId: string, page = 1, limit = 50) => {
    const response = await api.get(`/messages/${conversationId}?page=${page}&limit=${limit}`);
    return response.data;
  },

  // 流式发送消息
  sendMessageStream: async (conversationId: string, content: string, onChunk: (data: any) => void) => {
    const token = Cookies.get('token');
    const eventSource = new EventSource(
      `${API_BASE_URL}/messages/stream`,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );

    // 发送消息数据
    await fetch(`${API_BASE_URL}/messages/stream`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ conversationId, content }),
    });

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        onChunk(data);
        
        if (data.type === 'completed' || data.type === 'error') {
          eventSource.close();
        }
      } catch (error) {
        console.error('解析SSE数据失败:', error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('SSE连接错误:', error);
      eventSource.close();
    };

    return eventSource;
  },
};

// 系统相关API
export const systemAPI = {
  // 检查Ollama服务状态
  checkOllamaHealth: async () => {
    const response = await api.get('/system/ollama/health');
    return response.data;
  },

  // 获取可用模型列表
  getModels: async () => {
    const response = await api.get('/system/ollama/models');
    return response.data;
  },

  // 获取系统统计信息
  getStats: async (startDate?: string, endDate?: string) => {
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    const response = await api.get(`/system/stats?${params.toString()}`);
    return response.data;
  },
};

export default api;
