const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

async function recreateDatabase() {
  let connection;
  
  try {
    console.log('🚀 开始重建数据库...');

    // 连接到MySQL服务器（不指定数据库）
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || '************',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'sysdb',
      password: process.env.DB_PASSWORD || 'Roskfl2023',
      charset: 'utf8mb4'
    });

    console.log('✅ 数据库连接成功');

    const dbName = process.env.DB_NAME || 'xiaoaiPlus';

    // 删除现有数据库（如果存在）
    console.log(`🗑️  删除现有数据库 ${dbName}...`);
    await connection.execute(`DROP DATABASE IF EXISTS \`${dbName}\``);
    console.log('✅ 现有数据库已删除');

    // 创建新数据库
    console.log(`🆕 创建新数据库 ${dbName}...`);
    await connection.execute(`CREATE DATABASE \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log('✅ 新数据库创建成功');

    // 选择数据库
    await connection.query(`USE \`${dbName}\``);

    // 读取SQL初始化脚本
    const sqlPath = path.join(__dirname, '../../database/init.sql');
    console.log(`📖 读取SQL脚本: ${sqlPath}`);
    
    const sqlContent = await fs.readFile(sqlPath, 'utf8');
    console.log('📄 SQL文件内容长度:', sqlContent.length);

    // 分割SQL语句（按分号分割，忽略空行和注释）
    const allStatements = sqlContent.split(';').map(stmt => stmt.trim());
    console.log('🔍 分割后的语句数量:', allStatements.length);

    const sqlStatements = allStatements.filter((stmt, index) => {
        console.log(`🔍 检查语句 ${index + 1}: "${stmt.substring(0, 50)}..."`);
        if (stmt.length === 0) {
          console.log(`  ❌ 跳过空语句`);
          return false;
        }
        if (stmt.startsWith('--')) {
          console.log(`  ❌ 跳过注释`);
          return false;
        }
        if (stmt.startsWith('/*')) {
          console.log(`  ❌ 跳过块注释`);
          return false;
        }
        if (stmt.toUpperCase().includes('CREATE DATABASE')) {
          console.log(`  ❌ 跳过创建数据库语句`);
          return false;
        }
        if (stmt.toUpperCase().includes('USE XIAOAIPLUS')) {
          console.log(`  ❌ 跳过USE语句`);
          return false;
        }
        console.log(`  ✅ 保留语句`);
        return true;
      });

    console.log(`📝 找到 ${sqlStatements.length} 条SQL语句`);

    // 打印前几条语句用于调试
    if (sqlStatements.length > 0) {
      console.log('🔍 前3条语句:');
      sqlStatements.slice(0, 3).forEach((stmt, i) => {
        console.log(`  ${i + 1}: ${stmt.substring(0, 50)}...`);
      });
    }

    // 执行每条SQL语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i];
      
      try {
        await connection.query(statement);
        console.log(`✅ 执行SQL语句 ${i + 1}/${sqlStatements.length}`);
      } catch (error) {
        console.error(`❌ 执行SQL语句失败 ${i + 1}:`, error.message);
        console.error('SQL语句:', statement.substring(0, 100) + '...');
        // 继续执行其他语句
      }
    }

    // 验证表创建
    console.log('🔍 验证数据库表...');
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('📋 创建的表:');
    tables.forEach(table => {
      console.log(`  ✓ ${Object.values(table)[0]}`);
    });

    // 验证管理员账户
    console.log('👤 验证管理员账户...');
    const [adminUsers] = await connection.execute(
      'SELECT id, username, role, tokens FROM users WHERE role = "admin"'
    );
    
    if (adminUsers.length > 0) {
      console.log('✅ 管理员账户:');
      adminUsers.forEach(user => {
        console.log(`  - ID: ${user.id}, 用户名: ${user.username}, 令牌: ${user.tokens}`);
      });
    } else {
      console.log('⚠️  未找到管理员账户');
    }

    // 验证系统配置
    console.log('⚙️  验证系统配置...');
    const [configs] = await connection.execute('SELECT config_key, config_value FROM system_config');
    console.log('✅ 系统配置:');
    configs.forEach(config => {
      console.log(`  - ${config.config_key}: ${config.config_value}`);
    });

    console.log('🎉 数据库重建完成！');
    console.log('');
    console.log('📋 默认管理员账户信息:');
    console.log('   用户ID: 00001');
    console.log('   用户名: admin');
    console.log('   密码: admin123');
    console.log('   令牌: 999999');
    
  } catch (error) {
    console.error('❌ 数据库重建失败:', error.message);
    console.error('详细错误:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  recreateDatabase();
}

module.exports = recreateDatabase;
