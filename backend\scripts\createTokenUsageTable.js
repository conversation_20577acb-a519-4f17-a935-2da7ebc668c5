const mysql = require('mysql2/promise');
const config = require('../config/database');

async function createTokenUsageTable() {
  let connection;
  
  try {
    // 连接到数据库
    connection = await mysql.createConnection(config);
    console.log('✅ 数据库连接成功');

    // 创建token_usage表
    const createTokenUsageTableSQL = `
      CREATE TABLE IF NOT EXISTS token_usage (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(10) NOT NULL,
        action_type ENUM('consume', 'recharge') NOT NULL,
        amount INT NOT NULL,
        balance_before INT NOT NULL DEFAULT 0,
        balance_after INT NOT NULL DEFAULT 0,
        description VARCHAR(255) NOT NULL,
        admin_id VARCHAR(10) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_action_type (action_type),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    await connection.execute(createTokenUsageTableSQL);
    console.log('✅ token_usage表创建成功');

    // 插入一些示例数据
    const insertSampleData = `
      INSERT INTO token_usage (user_id, action_type, amount, balance_before, balance_after, description, admin_id) VALUES
      ('00001', 'recharge', 1000, 0, 1000, '初始充值', '00001'),
      ('00001', 'consume', 50, 1000, 950, 'AI对话消费', NULL),
      ('00001', 'consume', 30, 950, 920, 'AI对话消费', NULL),
      ('00001', 'recharge', 500, 920, 1420, '管理员充值', '00001'),
      ('00001', 'consume', 25, 1420, 1395, 'AI对话消费', NULL);
    `;

    await connection.execute(insertSampleData);
    console.log('✅ 示例数据插入成功');

    console.log('\n🎉 token_usage表创建完成！');
    
  } catch (error) {
    console.error('❌ 创建token_usage表失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('📝 数据库连接已关闭');
    }
  }
}

// 运行脚本
createTokenUsageTable();
