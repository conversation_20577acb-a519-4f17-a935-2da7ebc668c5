const { Ollama } = require('ollama');

class OllamaService {
  constructor() {
    this.ollama = new Ollama({
      host: process.env.OLLAMA_HOST || 'http://localhost:11434'
    });
    this.defaultModel = process.env.OLLAMA_MODEL || 'llama2';
  }

  // 检查Ollama服务是否可用
  async checkHealth() {
    try {
      const models = await this.ollama.list();
      return {
        status: 'healthy',
        models: models.models.map(model => model.name)
      };
    } catch (error) {
      console.error('Ollama健康检查失败:', error);
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  // 生成对话标题
  async generateTitle(userMessage) {
    try {
      const prompt = `请为以下用户问题生成一个简洁的对话标题（不超过20个字符）：

用户问题：${userMessage}

要求：
1. 标题要简洁明了
2. 能够概括问题的核心内容
3. 不超过20个字符
4. 只返回标题，不要其他内容

标题：`;

      const response = await this.ollama.generate({
        model: this.defaultModel,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.3,
          max_tokens: 50
        }
      });

      // 清理生成的标题
      let title = response.response.trim();
      
      // 移除可能的引号和多余字符
      title = title.replace(/^["']|["']$/g, '');
      title = title.replace(/\n.*$/s, ''); // 只取第一行
      
      // 限制长度
      if (title.length > 20) {
        title = title.substring(0, 17) + '...';
      }

      return title || '新对话';
    } catch (error) {
      console.error('生成标题失败:', error);
      return '新对话';
    }
  }

  // 生成AI回复
  async generateResponse(messages, options = {}) {
    try {
      const {
        model = this.defaultModel,
        temperature = 0.7,
        maxTokens = 2000
      } = options;

      // 构建对话历史
      const conversationHistory = messages.map(msg => {
        return `${msg.role === 'user' ? '用户' : 'AI助手'}：${msg.content}`;
      }).join('\n\n');

      // 构建系统提示
      const systemPrompt = `你是一个专为监狱服刑人员设计的AI学习和咨询助手。你的任务是：

1. 提供教育和学习支持
2. 回答法律相关问题
3. 提供心理健康建议
4. 协助技能学习和职业规划
5. 鼓励积极改造和重新融入社会

请注意：
- 保持积极正面的态度
- 提供准确、有用的信息
- 避免涉及违法或有害内容
- 鼓励学习和自我提升
- 语言要通俗易懂

对话历史：
${conversationHistory}

请根据以上对话历史，为最后一个用户问题提供有帮助的回复：`;

      const response = await this.ollama.generate({
        model: model,
        prompt: systemPrompt,
        stream: false,
        options: {
          temperature: temperature,
          max_tokens: maxTokens
        }
      });

      return {
        content: response.response.trim(),
        tokensUsed: this.estimateTokens(response.response)
      };
    } catch (error) {
      console.error('生成AI回复失败:', error);
      throw new Error('AI服务暂时不可用，请稍后重试');
    }
  }

  // 流式生成AI回复
  async generateStreamResponse(messages, options = {}) {
    try {
      const {
        model = this.defaultModel,
        temperature = 0.7,
        maxTokens = 2000
      } = options;

      // 构建对话历史
      const conversationHistory = messages.map(msg => {
        return `${msg.role === 'user' ? '用户' : 'AI助手'}：${msg.content}`;
      }).join('\n\n');

      const systemPrompt = `你是一个专为监狱服刑人员设计的AI学习和咨询助手。请根据对话历史为用户提供有帮助的回复。

对话历史：
${conversationHistory}

请回复：`;

      const stream = await this.ollama.generate({
        model: model,
        prompt: systemPrompt,
        stream: true,
        options: {
          temperature: temperature,
          max_tokens: maxTokens
        }
      });

      return stream;
    } catch (error) {
      console.error('生成流式AI回复失败:', error);
      throw new Error('AI服务暂时不可用，请稍后重试');
    }
  }

  // 估算令牌数量（简单估算）
  estimateTokens(text) {
    // 简单的令牌估算：中文字符按1个令牌计算，英文单词按平均4个字符计算
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    const otherChars = text.length - chineseChars - englishWords * 4;
    
    return Math.ceil(chineseChars + englishWords + otherChars / 4);
  }

  // 获取可用模型列表
  async getAvailableModels() {
    try {
      const models = await this.ollama.list();
      return models.models.map(model => ({
        name: model.name,
        size: model.size,
        modified_at: model.modified_at
      }));
    } catch (error) {
      console.error('获取模型列表失败:', error);
      return [];
    }
  }

  // 拉取新模型
  async pullModel(modelName) {
    try {
      const stream = await this.ollama.pull({
        model: modelName,
        stream: true
      });

      return stream;
    } catch (error) {
      console.error('拉取模型失败:', error);
      throw new Error(`拉取模型 ${modelName} 失败: ${error.message}`);
    }
  }
}

// 创建单例实例
const ollamaService = new OllamaService();

module.exports = ollamaService;
