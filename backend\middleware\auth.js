const jwt = require('jsonwebtoken');
const User = require('../models/User');

// 验证JWT令牌
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ 
        error: '访问被拒绝',
        message: '需要提供访问令牌'
      });
    }

    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 获取用户信息
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(401).json({ 
        error: '用户不存在',
        message: '令牌中的用户不存在'
      });
    }

    if (user.status !== 'active') {
      return res.status(401).json({ 
        error: '账户已被禁用',
        message: '您的账户已被管理员禁用'
      });
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: user.id,
      username: user.username,
      role: user.role,
      tokens: user.tokens
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        error: '无效的令牌',
        message: '提供的访问令牌无效'
      });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        error: '令牌已过期',
        message: '访问令牌已过期，请重新登录'
      });
    } else {
      console.error('认证错误:', error);
      return res.status(500).json({ 
        error: '服务器错误',
        message: '认证过程中发生错误'
      });
    }
  }
};

// 验证管理员权限
const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ 
      error: '未认证',
      message: '请先登录'
    });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({ 
      error: '权限不足',
      message: '需要管理员权限'
    });
  }

  next();
};

// 验证用户权限（用户只能访问自己的资源）
const requireOwnership = (req, res, next) => {
  const resourceUserId = req.params.userId || req.body.userId;
  
  if (!req.user) {
    return res.status(401).json({ 
      error: '未认证',
      message: '请先登录'
    });
  }

  // 管理员可以访问所有资源
  if (req.user.role === 'admin') {
    return next();
  }

  // 普通用户只能访问自己的资源
  if (req.user.id !== resourceUserId) {
    return res.status(403).json({ 
      error: '权限不足',
      message: '您只能访问自己的资源'
    });
  }

  next();
};

// 生成JWT令牌
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: '24h' }
  );
};

module.exports = {
  authenticateToken,
  requireAdmin,
  requireOwnership,
  generateToken
};
