const User = require('../models/User');
const { generateToken } = require('../middleware/auth');

// 用户登录
const login = async (req, res) => {
  try {
    const { userId, password } = req.body;

    if (!userId || !password) {
      return res.status(400).json({
        error: '参数错误',
        message: '用户ID和密码不能为空'
      });
    }

    // 查找用户
    const user = await User.findById(userId);
    if (!user) {
      return res.status(401).json({
        error: '登录失败',
        message: '用户ID或密码错误'
      });
    }

    // 验证密码
    const isValidPassword = await User.validatePassword(user, password);
    if (!isValidPassword) {
      return res.status(401).json({
        error: '登录失败',
        message: '用户ID或密码错误'
      });
    }

    // 检查用户状态
    if (user.status !== 'active') {
      return res.status(401).json({
        error: '账户已被禁用',
        message: '您的账户已被管理员禁用，请联系管理员'
      });
    }

    // 更新最后登录时间
    await User.updateLastLogin(userId);

    // 生成JWT令牌
    const token = generateToken(userId);

    res.json({
      message: '登录成功',
      token,
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        tokens: user.tokens
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '登录过程中发生错误'
    });
  }
};

// 获取用户信息
const getProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({
        error: '用户不存在',
        message: '找不到用户信息'
      });
    }

    res.json({
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        tokens: user.tokens,
        status: user.status,
        created_at: user.created_at,
        last_login: user.last_login
      }
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '获取用户信息时发生错误'
    });
  }
};

// 创建用户（管理员功能）
const createUser = async (req, res) => {
  try {
    const { username, password, tokens = 1000 } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        error: '参数错误',
        message: '用户名和密码不能为空'
      });
    }

    if (password.length < 6) {
      return res.status(400).json({
        error: '密码太短',
        message: '密码长度至少为6位'
      });
    }

    // 生成用户ID
    const userId = await User.generateUserId();

    // 创建用户
    const newUser = await User.create({
      id: userId,
      username,
      password,
      role: 'user',
      tokens
    });

    res.status(201).json({
      message: '用户创建成功',
      user: {
        id: newUser.id,
        username: newUser.username,
        role: newUser.role,
        tokens: newUser.tokens,
        created_at: newUser.created_at
      }
    });
  } catch (error) {
    console.error('创建用户错误:', error);
    
    if (error.message.includes('已存在')) {
      return res.status(409).json({
        error: '创建失败',
        message: error.message
      });
    }

    res.status(500).json({
      error: '服务器错误',
      message: '创建用户时发生错误'
    });
  }
};

// 获取所有用户（管理员功能）
const getAllUsers = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    const result = await User.getAll(page, limit);

    res.json({
      message: '获取用户列表成功',
      ...result
    });
  } catch (error) {
    console.error('获取用户列表错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '获取用户列表时发生错误'
    });
  }
};

// 充值令牌（管理员功能）
const rechargeTokens = async (req, res) => {
  try {
    const { userId } = req.params;
    const { amount, description = '管理员充值' } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        error: '参数错误',
        message: '充值金额必须大于0'
      });
    }

    // 检查目标用户是否存在
    const targetUser = await User.findById(userId);
    if (!targetUser) {
      return res.status(404).json({
        error: '用户不存在',
        message: '找不到要充值的用户'
      });
    }

    // 执行充值
    const newBalance = await User.updateTokens(
      userId,
      amount,
      'recharge',
      description,
      req.user.id
    );

    res.json({
      message: '充值成功',
      userId,
      amount,
      newBalance,
      description
    });
  } catch (error) {
    console.error('充值令牌错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '充值过程中发生错误'
    });
  }
};

// 更新用户状态（管理员功能）
const updateUserStatus = async (req, res) => {
  try {
    const { userId } = req.params;
    const { status } = req.body;

    if (!['active', 'inactive', 'banned'].includes(status)) {
      return res.status(400).json({
        error: '参数错误',
        message: '无效的用户状态'
      });
    }

    const updatedUser = await User.update(userId, { status });
    if (!updatedUser) {
      return res.status(404).json({
        error: '用户不存在',
        message: '找不到要更新的用户'
      });
    }

    res.json({
      message: '用户状态更新成功',
      user: {
        id: updatedUser.id,
        username: updatedUser.username,
        status: updatedUser.status
      }
    });
  } catch (error) {
    console.error('更新用户状态错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '更新用户状态时发生错误'
    });
  }
};

module.exports = {
  login,
  getProfile,
  createUser,
  getAllUsers,
  rechargeTokens,
  updateUserStatus
};
