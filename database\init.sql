-- AI学习和咨询系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS xiaoaiPlus CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE xiaoaiPlus;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(5) PRIMARY KEY COMMENT '5位数字用户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    role ENUM('user', 'admin') DEFAULT 'user' COMMENT '用户角色',
    tokens INT DEFAULT 1000 COMMENT '令牌余额',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '用户状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login TIMESTAMP NULL COMMENT '最后登录时间',
    
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='用户表';

-- 对话表
CREATE TABLE IF NOT EXISTS conversations (
    id VARCHAR(36) PRIMARY KEY COMMENT '对话ID (UUID)',
    user_id VARCHAR(5) NOT NULL COMMENT '用户ID',
    title VARCHAR(200) NOT NULL COMMENT '对话标题',
    status ENUM('active', 'archived', 'deleted') DEFAULT 'active' COMMENT '对话状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='对话表';

-- 消息表
CREATE TABLE IF NOT EXISTS messages (
    id VARCHAR(36) PRIMARY KEY COMMENT '消息ID (UUID)',
    conversation_id VARCHAR(36) NOT NULL COMMENT '对话ID',
    user_id VARCHAR(5) NOT NULL COMMENT '用户ID',
    role ENUM('user', 'assistant') NOT NULL COMMENT '消息角色',
    content TEXT NOT NULL COMMENT '消息内容',
    tokens_used INT DEFAULT 0 COMMENT '消耗的令牌数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role (role),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='消息表';

-- 令牌使用记录表
CREATE TABLE IF NOT EXISTS token_usage (
    id VARCHAR(36) PRIMARY KEY COMMENT '记录ID (UUID)',
    user_id VARCHAR(5) NOT NULL COMMENT '用户ID',
    action_type ENUM('consume', 'recharge') NOT NULL COMMENT '操作类型',
    amount INT NOT NULL COMMENT '令牌数量',
    balance_before INT NOT NULL COMMENT '操作前余额',
    balance_after INT NOT NULL COMMENT '操作后余额',
    description VARCHAR(255) COMMENT '操作描述',
    admin_id VARCHAR(5) NULL COMMENT '管理员ID（充值时）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='令牌使用记录表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    config_key VARCHAR(50) PRIMARY KEY COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='系统配置表';

-- 插入默认系统配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('max_conversations_per_user', '10', '每个用户最大对话数量'),
('default_tokens', '1000', '新用户默认令牌数'),
('tokens_per_message', '10', '每条消息消耗的令牌数'),
('ollama_model', 'llama2', '默认使用的Ollama模型')
ON DUPLICATE KEY UPDATE config_value = VALUES(config_value);

-- 创建默认管理员账户 (ID: 00001, 密码: admin123)
INSERT INTO users (id, username, password_hash, role, tokens) VALUES
('00001', 'admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 999999)
ON DUPLICATE KEY UPDATE username = VALUES(username);
