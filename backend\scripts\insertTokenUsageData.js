const mysql = require('mysql2/promise');

async function insertSampleData() {
  const connection = await mysql.createConnection({
    host: '************',
    user: 'sysdb',
    password: 'Roskfl2023',
    database: 'xiaoaiPlus'
  });
  
  try {
    // 插入示例数据
    const insertData = [
      {
        id: '1',
        user_id: '00001',
        action_type: 'recharge',
        amount: 1000,
        balance_before: 0,
        balance_after: 1000,
        description: '初始充值',
        admin_id: '00001'
      },
      {
        id: '2',
        user_id: '00001',
        action_type: 'consume',
        amount: 50,
        balance_before: 1000,
        balance_after: 950,
        description: 'AI对话消费',
        admin_id: null
      },
      {
        id: '3',
        user_id: '00001',
        action_type: 'consume',
        amount: 30,
        balance_before: 950,
        balance_after: 920,
        description: 'AI对话消费',
        admin_id: null
      },
      {
        id: '4',
        user_id: '00001',
        action_type: 'recharge',
        amount: 500,
        balance_before: 920,
        balance_after: 1420,
        description: '管理员充值',
        admin_id: '00001'
      },
      {
        id: '5',
        user_id: '00001',
        action_type: 'consume',
        amount: 25,
        balance_before: 1420,
        balance_after: 1395,
        description: 'AI对话消费',
        admin_id: null
      }
    ];

    for (const record of insertData) {
      await connection.execute(
        'INSERT INTO token_usage (id, user_id, action_type, amount, balance_before, balance_after, description, admin_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [record.id, record.user_id, record.action_type, record.amount, record.balance_before, record.balance_after, record.description, record.admin_id]
      );
    }

    console.log('✅ 示例数据插入成功');
    
    // 验证插入的数据
    const [rows] = await connection.execute('SELECT COUNT(*) as count FROM token_usage');
    console.log('数据行数:', rows[0].count);
    
  } catch (error) {
    console.error('❌ 插入数据失败:', error.message);
  } finally {
    await connection.end();
  }
}

insertSampleData();
