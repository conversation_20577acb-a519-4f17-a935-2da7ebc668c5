const mysql = require('mysql2/promise');

async function checkUsersTable() {
  const connection = await mysql.createConnection({
    host: '************',
    user: 'sysdb',
    password: 'Roskfl2023',
    database: 'xiaoaiPlus'
  });
  
  try {
    // 检查用户表结构
    const [columns] = await connection.execute('DESCRIBE users');
    console.log('用户表结构:');
    columns.forEach(col => {
      console.log(`  ${col.Field}: ${col.Type} ${col.Null} ${col.Key} ${col.Default} ${col.Extra}`);
    });
    
    // 查看现有用户数据
    const [users] = await connection.execute('SELECT * FROM users LIMIT 5');
    console.log('\n现有用户数据:');
    users.forEach(user => {
      console.log(`  ID: ${user.id}, Username: ${user.username || 'N/A'}, Role: ${user.role}`);
    });
    
  } catch (error) {
    console.error('检查失败:', error.message);
  } finally {
    await connection.end();
  }
}

checkUsersTable();
