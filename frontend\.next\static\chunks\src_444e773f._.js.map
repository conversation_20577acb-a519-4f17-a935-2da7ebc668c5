{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9/XAP/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport Cookies from 'js-cookie';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器 - 添加认证token\napi.interceptors.request.use(\n  (config) => {\n    const token = Cookies.get('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器 - 处理错误\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token过期或无效，清除本地存储并跳转到登录页\n      Cookies.remove('token');\n      Cookies.remove('user');\n      if (typeof window !== 'undefined') {\n        window.location.href = '/login';\n      }\n    }\n    return Promise.reject(error);\n  }\n);\n\n// 用户相关API\nexport const userAPI = {\n  // 登录\n  login: async (userId: string, password: string) => {\n    const response = await api.post('/users/login', { userId, password });\n    return response.data;\n  },\n\n  // 获取用户信息\n  getProfile: async () => {\n    const response = await api.get('/users/profile');\n    return response.data;\n  },\n\n  // 创建用户（管理员）\n  createUser: async (userData: { username: string; password: string; tokens?: number }) => {\n    const response = await api.post('/users', userData);\n    return response.data;\n  },\n\n  // 获取所有用户（管理员）\n  getAllUsers: async (page = 1, limit = 20) => {\n    const response = await api.get(`/users?page=${page}&limit=${limit}`);\n    return response.data;\n  },\n\n  // 充值令牌（管理员）\n  rechargeTokens: async (userId: string, amount: number, description?: string) => {\n    const response = await api.post(`/users/${userId}/recharge`, { amount, description });\n    return response.data;\n  },\n\n  // 更新用户状态（管理员）\n  updateUserStatus: async (userId: string, status: 'active' | 'inactive' | 'banned') => {\n    const response = await api.patch(`/users/${userId}/status`, { status });\n    return response.data;\n  },\n};\n\n// 对话相关API\nexport const conversationAPI = {\n  // 创建对话\n  create: async (title: string) => {\n    const response = await api.post('/conversations', { title });\n    return response.data;\n  },\n\n  // 获取用户对话列表\n  getUserConversations: async (status = 'active') => {\n    const response = await api.get(`/conversations/my?status=${status}`);\n    return response.data;\n  },\n\n  // 获取对话详情\n  getConversation: async (conversationId: string, page = 1, limit = 50) => {\n    const response = await api.get(`/conversations/${conversationId}?page=${page}&limit=${limit}`);\n    return response.data;\n  },\n\n  // 更新对话标题\n  updateTitle: async (conversationId: string, title: string) => {\n    const response = await api.patch(`/conversations/${conversationId}/title`, { title });\n    return response.data;\n  },\n\n  // 删除对话\n  delete: async (conversationId: string) => {\n    const response = await api.delete(`/conversations/${conversationId}`);\n    return response.data;\n  },\n\n  // 获取所有对话（管理员）\n  getAllConversations: async (page = 1, limit = 20, filters?: { userId?: string; status?: string }) => {\n    const params = new URLSearchParams({ page: page.toString(), limit: limit.toString() });\n    if (filters?.userId) params.append('userId', filters.userId);\n    if (filters?.status) params.append('status', filters.status);\n    \n    const response = await api.get(`/conversations?${params.toString()}`);\n    return response.data;\n  },\n};\n\n// 消息相关API\nexport const messageAPI = {\n  // 发送消息\n  sendMessage: async (conversationId: string, content: string) => {\n    const response = await api.post('/messages', { conversationId, content });\n    return response.data;\n  },\n\n  // 获取对话消息\n  getMessages: async (conversationId: string, page = 1, limit = 50) => {\n    const response = await api.get(`/messages/${conversationId}?page=${page}&limit=${limit}`);\n    return response.data;\n  },\n\n  // 流式发送消息\n  sendMessageStream: async (conversationId: string, content: string, onChunk: (data: any) => void) => {\n    const token = Cookies.get('token');\n    const eventSource = new EventSource(\n      `${API_BASE_URL}/messages/stream`,\n      {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n      }\n    );\n\n    // 发送消息数据\n    await fetch(`${API_BASE_URL}/messages/stream`, {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ conversationId, content }),\n    });\n\n    eventSource.onmessage = (event) => {\n      try {\n        const data = JSON.parse(event.data);\n        onChunk(data);\n        \n        if (data.type === 'completed' || data.type === 'error') {\n          eventSource.close();\n        }\n      } catch (error) {\n        console.error('解析SSE数据失败:', error);\n      }\n    };\n\n    eventSource.onerror = (error) => {\n      console.error('SSE连接错误:', error);\n      eventSource.close();\n    };\n\n    return eventSource;\n  },\n};\n\n// 系统相关API\nexport const systemAPI = {\n  // 检查Ollama服务状态\n  checkOllamaHealth: async () => {\n    const response = await api.get('/system/ollama/health');\n    return response.data;\n  },\n\n  // 获取可用模型列表\n  getModels: async () => {\n    const response = await api.get('/system/ollama/models');\n    return response.data;\n  },\n\n  // 获取系统统计信息\n  getStats: async (startDate?: string, endDate?: string) => {\n    const params = new URLSearchParams();\n    if (startDate) params.append('startDate', startDate);\n    if (endDate) params.append('endDate', endDate);\n    \n    const response = await api.get(`/system/stats?${params.toString()}`);\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;AAGqB;;;;;;;;;;;;;AAArB,MAAM,eAAe,iEAAmC;AAExD,YAAY;AACZ,MAAM,MAAM,MAAM,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,oBAAoB;AACpB,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,QAAQ,GAAG,CAAC;IAC1B,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,eAAe;AACf,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,2BAA2B;QAC3B,QAAQ,MAAM,CAAC;QACf,QAAQ,MAAM,CAAC;QACf,wCAAmC;YACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,KAAK;IACL,OAAO,OAAO,QAAgB;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,gBAAgB;YAAE;YAAQ;QAAS;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,YAAY;QACV,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,UAAU;QAC1C,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,aAAa,OAAO,OAAO,CAAC,EAAE,QAAQ,EAAE;QACtC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK,OAAO,EAAE,OAAO;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,gBAAgB,OAAO,QAAgB,QAAgB;QACrD,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,SAAS,CAAC,EAAE;YAAE;YAAQ;QAAY;QACnF,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,kBAAkB,OAAO,QAAgB;QACvC,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,EAAE;YAAE;QAAO;QACrE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,kBAAkB;IAC7B,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,kBAAkB;YAAE;QAAM;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,sBAAsB,OAAO,SAAS,QAAQ;QAC5C,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,yBAAyB,EAAE,QAAQ;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,iBAAiB,OAAO,gBAAwB,OAAO,CAAC,EAAE,QAAQ,EAAE;QAClE,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,eAAe,EAAE,eAAe,MAAM,EAAE,KAAK,OAAO,EAAE,OAAO;QAC7F,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,aAAa,OAAO,gBAAwB;QAC1C,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,CAAC,eAAe,EAAE,eAAe,MAAM,CAAC,EAAE;YAAE;QAAM;QACnF,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;IACP,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,eAAe,EAAE,gBAAgB;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,qBAAqB,OAAO,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE;QAChD,MAAM,SAAS,IAAI,gBAAgB;YAAE,MAAM,KAAK,QAAQ;YAAI,OAAO,MAAM,QAAQ;QAAG;QACpF,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC3D,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAE3D,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,eAAe,EAAE,OAAO,QAAQ,IAAI;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,aAAa;IACxB,OAAO;IACP,aAAa,OAAO,gBAAwB;QAC1C,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;YAAE;YAAgB;QAAQ;QACvE,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,aAAa,OAAO,gBAAwB,OAAO,CAAC,EAAE,QAAQ,EAAE;QAC9D,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,eAAe,MAAM,EAAE,KAAK,OAAO,EAAE,OAAO;QACxF,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,mBAAmB,OAAO,gBAAwB,SAAiB;QACjE,MAAM,QAAQ,QAAQ,GAAG,CAAC;QAC1B,MAAM,cAAc,IAAI,YACtB,GAAG,aAAa,gBAAgB,CAAC,EACjC;YACE,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAClC,gBAAgB;YAClB;QACF;QAGF,SAAS;QACT,MAAM,MAAM,GAAG,aAAa,gBAAgB,CAAC,EAAE;YAC7C,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAClC,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAgB;YAAQ;QACjD;QAEA,YAAY,SAAS,GAAG,CAAC;YACvB,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;gBAClC,QAAQ;gBAER,IAAI,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI,KAAK,SAAS;oBACtD,YAAY,KAAK;gBACnB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,cAAc;YAC9B;QACF;QAEA,YAAY,OAAO,GAAG,CAAC;YACrB,QAAQ,KAAK,CAAC,YAAY;YAC1B,YAAY,KAAK;QACnB;QAEA,OAAO;IACT;AACF;AAGO,MAAM,YAAY;IACvB,eAAe;IACf,mBAAmB;QACjB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,WAAW;QACT,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,UAAU,OAAO,WAAoB;QACnC,MAAM,SAAS,IAAI;QACnB,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;QAC1C,IAAI,SAAS,OAAO,MAAM,CAAC,WAAW;QAEtC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,QAAQ,IAAI;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9/XAP/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport Cookies from 'js-cookie';\nimport { userAPI } from '@/lib/api';\nimport { message } from 'antd';\n\ninterface User {\n  id: string;\n  username: string;\n  role: 'user' | 'admin';\n  tokens: number;\n  status: string;\n  created_at: string;\n  last_login?: string;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  login: (userId: string, password: string) => Promise<boolean>;\n  logout: () => void;\n  refreshUser: () => Promise<void>;\n  isAdmin: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // 检查本地存储的用户信息\n  useEffect(() => {\n    const initAuth = async () => {\n      try {\n        const token = Cookies.get('token');\n        const savedUser = Cookies.get('user');\n\n        if (token && savedUser) {\n          const userData = JSON.parse(savedUser);\n          setUser(userData);\n          \n          // 验证token是否仍然有效\n          try {\n            await refreshUser();\n          } catch (error) {\n            // Token无效，清除本地存储\n            logout();\n          }\n        }\n      } catch (error) {\n        console.error('初始化认证状态失败:', error);\n        logout();\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    initAuth();\n  }, []);\n\n  const login = async (userId: string, password: string): Promise<boolean> => {\n    try {\n      setLoading(true);\n      const response = await userAPI.login(userId, password);\n      \n      if (response.token && response.user) {\n        // 保存token和用户信息\n        Cookies.set('token', response.token, { expires: 1 }); // 1天过期\n        Cookies.set('user', JSON.stringify(response.user), { expires: 1 });\n        \n        setUser(response.user);\n        message.success('登录成功');\n        return true;\n      }\n      \n      return false;\n    } catch (error: any) {\n      console.error('登录失败:', error);\n      const errorMessage = error.response?.data?.message || '登录失败，请检查用户ID和密码';\n      message.error(errorMessage);\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const logout = () => {\n    Cookies.remove('token');\n    Cookies.remove('user');\n    setUser(null);\n    message.info('已退出登录');\n  };\n\n  const refreshUser = async () => {\n    try {\n      const response = await userAPI.getProfile();\n      if (response.user) {\n        setUser(response.user);\n        Cookies.set('user', JSON.stringify(response.user), { expires: 1 });\n      }\n    } catch (error) {\n      console.error('刷新用户信息失败:', error);\n      throw error;\n    }\n  };\n\n  const isAdmin = user?.role === 'admin';\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    login,\n    logout,\n    refreshUser,\n    isAdmin,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;AAEA;AACA;;;AALA;;;;;AA0BA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,cAAc;IACd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;mDAAW;oBACf,IAAI;wBACF,MAAM,QAAQ,QAAQ,GAAG,CAAC;wBAC1B,MAAM,YAAY,QAAQ,GAAG,CAAC;wBAE9B,IAAI,SAAS,WAAW;4BACtB,MAAM,WAAW,KAAK,KAAK,CAAC;4BAC5B,QAAQ;4BAER,gBAAgB;4BAChB,IAAI;gCACF,MAAM;4BACR,EAAE,OAAO,OAAO;gCACd,iBAAiB;gCACjB;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,cAAc;wBAC5B;oBACF,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,QAAgB;QACnC,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC,QAAQ;YAE7C,IAAI,SAAS,KAAK,IAAI,SAAS,IAAI,EAAE;gBACnC,eAAe;gBACf,QAAQ,GAAG,CAAC,SAAS,SAAS,KAAK,EAAE;oBAAE,SAAS;gBAAE,IAAI,OAAO;gBAC7D,QAAQ,GAAG,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI,GAAG;oBAAE,SAAS;gBAAE;gBAEhE,QAAQ,SAAS,IAAI;gBACrB,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW;YACtD,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd,OAAO;QACT,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS;QACb,QAAQ,MAAM,CAAC;QACf,QAAQ,MAAM,CAAC;QACf,QAAQ;QACR,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;IACf;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU;YACzC,IAAI,SAAS,IAAI,EAAE;gBACjB,QAAQ,SAAS,IAAI;gBACrB,QAAQ,GAAG,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI,GAAG;oBAAE,SAAS;gBAAE;YAClE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR;IACF;IAEA,MAAM,UAAU,MAAM,SAAS;IAE/B,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAhGa;KAAA", "debugId": null}}]}