const express = require('express');
const router = express.Router();
const messageController = require('../controllers/messageController');
const { authenticateToken } = require('../middleware/auth');

// 发送消息
router.post('/', authenticateToken, messageController.sendMessage);

// 流式发送消息
router.post('/stream', authenticateToken, messageController.sendMessageStream);

// 获取对话消息
router.get('/:conversationId', authenticateToken, messageController.getMessages);

module.exports = router;
