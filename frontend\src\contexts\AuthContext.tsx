'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import Cookies from 'js-cookie';
import { userAPI } from '@/lib/api';
import { message } from 'antd';

interface User {
  id: string;
  username: string;
  role: 'user' | 'admin';
  tokens: number;
  status: string;
  created_at: string;
  last_login?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  refreshUser: () => Promise<void>;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // 检查本地存储的用户信息
  useEffect(() => {
    const initAuth = async () => {
      try {
        const token = Cookies.get('token');
        const savedUser = Cookies.get('user');

        if (token && savedUser) {
          const userData = JSON.parse(savedUser);
          setUser(userData);
          
          // 验证token是否仍然有效
          try {
            await refreshUser();
          } catch (error) {
            // Token无效，清除本地存储
            logout();
          }
        }
      } catch (error) {
        console.error('初始化认证状态失败:', error);
        logout();
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await userAPI.login(username, password);
      
      if (response.token && response.user) {
        // 保存token和用户信息
        Cookies.set('token', response.token, { expires: 1 }); // 1天过期
        Cookies.set('user', JSON.stringify(response.user), { expires: 1 });
        
        setUser(response.user);
        message.success('登录成功');
        return true;
      }
      
      return false;
    } catch (error: any) {
      console.error('登录失败:', error);
      const errorMessage = error.response?.data?.message || '登录失败，请检查用户ID和密码';
      message.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    Cookies.remove('token');
    Cookies.remove('user');
    setUser(null);
    message.info('已退出登录');
  };

  const refreshUser = async () => {
    try {
      const response = await userAPI.getProfile();
      if (response.user) {
        setUser(response.user);
        Cookies.set('user', JSON.stringify(response.user), { expires: 1 });
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error);
      throw error;
    }
  };

  const isAdmin = user?.role === 'admin';

  const value: AuthContextType = {
    user,
    loading,
    login,
    logout,
    refreshUser,
    isAdmin,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
