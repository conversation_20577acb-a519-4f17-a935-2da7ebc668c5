"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conversationAPI: () => (/* binding */ conversationAPI),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   messageAPI: () => (/* binding */ messageAPI),\n/* harmony export */   systemAPI: () => (/* binding */ systemAPI),\n/* harmony export */   userAPI: () => (/* binding */ userAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n\nconst API_BASE_URL = \"http://localhost:3001/api\" || 0;\n// 创建axios实例\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 30000,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// 请求拦截器 - 添加认证token\napi.interceptors.request.use((config)=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('token');\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器 - 处理错误\napi.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    var _error_response;\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        // Token过期或无效，清除本地存储并跳转到登录页\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove('token');\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove('user');\n        if (true) {\n            window.location.href = '/login';\n        }\n    }\n    return Promise.reject(error);\n});\n// 用户相关API\nconst userAPI = {\n    // 登录\n    login: async (username, password)=>{\n        const response = await api.post('/users/login', {\n            username,\n            password\n        });\n        return response.data;\n    },\n    // 获取用户信息\n    getProfile: async ()=>{\n        const response = await api.get('/users/profile');\n        return response.data;\n    },\n    // 创建用户（管理员）\n    createUser: async (userData)=>{\n        const response = await api.post('/users', userData);\n        return response.data;\n    },\n    // 获取所有用户（管理员）\n    getAllUsers: async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        const response = await api.get(\"/users?page=\".concat(page, \"&limit=\").concat(limit));\n        return response.data;\n    },\n    // 充值令牌（管理员）\n    rechargeTokens: async (userId, amount, description)=>{\n        const response = await api.post(\"/users/\".concat(userId, \"/recharge\"), {\n            amount,\n            description\n        });\n        return response.data;\n    },\n    // 更新用户状态（管理员）\n    updateUserStatus: async (userId, status)=>{\n        const response = await api.patch(\"/users/\".concat(userId, \"/status\"), {\n            status\n        });\n        return response.data;\n    },\n    // 获取用户令牌使用记录（管理员）\n    getTokenUsage: async function(userId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20, filters = arguments.length > 3 ? arguments[3] : void 0;\n        let url = \"/users/\".concat(userId, \"/token-usage?page=\").concat(page, \"&limit=\").concat(limit);\n        if ((filters === null || filters === void 0 ? void 0 : filters.actionType) && filters.actionType !== 'all') {\n            url += \"&actionType=\".concat(filters.actionType);\n        }\n        if (filters === null || filters === void 0 ? void 0 : filters.startDate) {\n            url += \"&startDate=\".concat(filters.startDate);\n        }\n        if (filters === null || filters === void 0 ? void 0 : filters.endDate) {\n            url += \"&endDate=\".concat(filters.endDate);\n        }\n        const response = await api.get(url);\n        return response.data;\n    },\n    // 检查用户名是否可用（管理员）\n    checkUsernameAvailability: async (username)=>{\n        try {\n            const response = await api.get(\"/users/check-username/\".concat(username));\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 409) {\n                return {\n                    available: false,\n                    message: '用户名已存在'\n                };\n            }\n            throw error;\n        }\n    }\n};\n// 对话相关API\nconst conversationAPI = {\n    // 创建对话\n    create: async (title)=>{\n        const response = await api.post('/conversations', {\n            title\n        });\n        return response.data;\n    },\n    // 获取用户对话列表\n    getUserConversations: async function() {\n        let status = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'active';\n        const response = await api.get(\"/conversations/my?status=\".concat(status));\n        return response.data;\n    },\n    // 获取对话详情\n    getConversation: async function(conversationId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 50;\n        const response = await api.get(\"/conversations/\".concat(conversationId, \"?page=\").concat(page, \"&limit=\").concat(limit));\n        return response.data;\n    },\n    // 更新对话标题\n    updateTitle: async (conversationId, title)=>{\n        const response = await api.patch(\"/conversations/\".concat(conversationId, \"/title\"), {\n            title\n        });\n        return response.data;\n    },\n    // 删除对话\n    delete: async (conversationId)=>{\n        const response = await api.delete(\"/conversations/\".concat(conversationId));\n        return response.data;\n    },\n    // 获取所有对话（管理员）\n    getAllConversations: async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, filters = arguments.length > 2 ? arguments[2] : void 0;\n        const params = new URLSearchParams({\n            page: page.toString(),\n            limit: limit.toString()\n        });\n        if (filters === null || filters === void 0 ? void 0 : filters.userId) params.append('userId', filters.userId);\n        if (filters === null || filters === void 0 ? void 0 : filters.status) params.append('status', filters.status);\n        const response = await api.get(\"/conversations?\".concat(params.toString()));\n        return response.data;\n    }\n};\n// 消息相关API\nconst messageAPI = {\n    // 发送消息\n    sendMessage: async (conversationId, content)=>{\n        const response = await api.post('/messages', {\n            conversationId,\n            content\n        });\n        return response.data;\n    },\n    // 获取对话消息\n    getMessages: async function(conversationId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 50;\n        const response = await api.get(\"/messages/\".concat(conversationId, \"?page=\").concat(page, \"&limit=\").concat(limit));\n        return response.data;\n    },\n    // 流式发送消息\n    sendMessageStream: async (conversationId, content, onChunk)=>{\n        const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('token');\n        const eventSource = new EventSource(\"\".concat(API_BASE_URL, \"/messages/stream\"), {\n            headers: {\n                'Authorization': \"Bearer \".concat(token),\n                'Content-Type': 'application/json'\n            }\n        });\n        // 发送消息数据\n        await fetch(\"\".concat(API_BASE_URL, \"/messages/stream\"), {\n            method: 'POST',\n            headers: {\n                'Authorization': \"Bearer \".concat(token),\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                conversationId,\n                content\n            })\n        });\n        eventSource.onmessage = (event)=>{\n            try {\n                const data = JSON.parse(event.data);\n                onChunk(data);\n                if (data.type === 'completed' || data.type === 'error') {\n                    eventSource.close();\n                }\n            } catch (error) {\n                console.error('解析SSE数据失败:', error);\n            }\n        };\n        eventSource.onerror = (error)=>{\n            console.error('SSE连接错误:', error);\n            eventSource.close();\n        };\n        return eventSource;\n    }\n};\n// 系统相关API\nconst systemAPI = {\n    // 检查Ollama服务状态\n    checkOllamaHealth: async ()=>{\n        const response = await api.get('/system/ollama/health');\n        return response.data;\n    },\n    // 获取可用模型列表\n    getModels: async ()=>{\n        const response = await api.get('/system/ollama/models');\n        return response.data;\n    },\n    // 获取系统统计信息\n    getStats: async (startDate, endDate)=>{\n        const params = new URLSearchParams();\n        if (startDate) params.append('startDate', startDate);\n        if (endDate) params.append('endDate', endDate);\n        const response = await api.get(\"/system/stats?\".concat(params.toString()));\n        return response.data;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});