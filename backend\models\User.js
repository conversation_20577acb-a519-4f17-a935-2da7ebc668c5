const { query } = require('../config/database');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

class User {
  // 根据ID查找用户
  static async findById(id) {
    const sql = 'SELECT * FROM users WHERE id = ?';
    const users = await query(sql, [id]);
    return users[0] || null;
  }

  // 根据用户名查找用户
  static async findByUsername(username) {
    const sql = 'SELECT * FROM users WHERE username = ?';
    const users = await query(sql, [username]);
    return users[0] || null;
  }

  // 创建新用户
  static async create(userData) {
    const { id, username, password, role = 'user', tokens = 1000 } = userData;
    
    // 检查用户ID是否已存在
    const existingUser = await this.findById(id);
    if (existingUser) {
      throw new Error('用户ID已存在');
    }

    // 检查用户名是否已存在
    const existingUsername = await this.findByUsername(username);
    if (existingUsername) {
      throw new Error('用户名已存在');
    }

    // 加密密码
    const passwordHash = await bcrypt.hash(password, 10);

    const sql = `
      INSERT INTO users (id, username, password_hash, role, tokens)
      VALUES (?, ?, ?, ?, ?)
    `;
    
    await query(sql, [id, username, passwordHash, role, tokens]);
    return await this.findById(id);
  }

  // 验证密码
  static async validatePassword(user, password) {
    return await bcrypt.compare(password, user.password_hash);
  }

  // 更新用户信息
  static async update(id, updateData) {
    const fields = [];
    const values = [];

    Object.keys(updateData).forEach(key => {
      if (key !== 'id' && updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });

    if (fields.length === 0) {
      throw new Error('没有要更新的字段');
    }

    values.push(id);
    const sql = `UPDATE users SET ${fields.join(', ')} WHERE id = ?`;
    
    await query(sql, values);
    return await this.findById(id);
  }

  // 更新令牌余额
  static async updateTokens(userId, amount, actionType = 'consume', description = '', adminId = null) {
    const user = await this.findById(userId);
    if (!user) {
      throw new Error('用户不存在');
    }

    const balanceBefore = user.tokens;
    let balanceAfter;

    if (actionType === 'consume') {
      if (balanceBefore < amount) {
        throw new Error('令牌余额不足');
      }
      balanceAfter = balanceBefore - amount;
    } else if (actionType === 'recharge') {
      balanceAfter = balanceBefore + amount;
    } else {
      throw new Error('无效的操作类型');
    }

    // 更新用户令牌余额
    await this.update(userId, { tokens: balanceAfter });

    // 记录令牌使用记录
    const recordSql = `
      INSERT INTO token_usage (id, user_id, action_type, amount, balance_before, balance_after, description, admin_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await query(recordSql, [
      uuidv4(),
      userId,
      actionType,
      amount,
      balanceBefore,
      balanceAfter,
      description,
      adminId
    ]);

    return balanceAfter;
  }

  // 获取所有用户（管理员功能）
  static async getAll(page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const countSql = 'SELECT COUNT(*) as total FROM users';
    const countResult = await query(countSql);
    const total = countResult[0].total;

    const sql = `
      SELECT id, username, role, tokens, status, created_at, last_login
      FROM users
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const users = await query(sql, [limit, offset]);

    return {
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  // 更新最后登录时间
  static async updateLastLogin(id) {
    const sql = 'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?';
    await query(sql, [id]);
  }

  // 生成新的5位数用户ID
  static async generateUserId() {
    let attempts = 0;
    const maxAttempts = 100;

    while (attempts < maxAttempts) {
      // 生成10000-99999之间的随机数
      const id = Math.floor(Math.random() * 90000) + 10000;
      const idStr = id.toString();

      // 检查ID是否已存在
      const existingUser = await this.findById(idStr);
      if (!existingUser) {
        return idStr;
      }

      attempts++;
    }

    throw new Error('无法生成唯一的用户ID');
  }

  // 通用查询方法
  static async query(sql, params = []) {
    return await query(sql, params);
  }
}

module.exports = User;
