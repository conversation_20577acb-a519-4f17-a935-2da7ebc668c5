'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  message,
  Popconfirm,
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Tooltip,
  Badge
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DollarOutlined,
  StopOutlined,
  CheckCircleOutlined,
  UserOutlined,
  ReloadOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { userAPI } from '@/lib/api';
import type { ColumnsType } from 'antd/es/table';
import TokenUsageModal from './TokenUsageModal';

const { Title, Text } = Typography;
const { Option } = Select;

interface User {
  id: string;
  username: string;
  role: 'user' | 'admin';
  tokens: number;
  status: 'active' | 'inactive' | 'banned';
  created_at: string;
  last_login?: string;
}

interface UserStats {
  total_users: number;
  active_users: number;
  admin_users: number;
  total_tokens: number;
  avg_tokens: number;
}

export default function UserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [stats, setStats] = useState<UserStats | null>(null);

  // 模态框状态
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [rechargeModalVisible, setRechargeModalVisible] = useState(false);
  const [tokenUsageModalVisible, setTokenUsageModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  // 表单实例
  const [createForm] = Form.useForm();
  const [rechargeForm] = Form.useForm();

  // 用户名检查状态
  const [usernameCheckStatus, setUsernameCheckStatus] = useState<'success' | 'error' | 'validating' | ''>('');
  const [usernameCheckMessage, setUsernameCheckMessage] = useState('');

  // 加载用户列表
  const loadUsers = async (page = 1, pageSize = 20) => {
    try {
      setLoading(true);
      const response = await userAPI.getAllUsers(page, pageSize);
      setUsers(response.users);
      setPagination({
        current: page,
        pageSize,
        total: response.pagination.total,
      });
    } catch (error) {
      console.error('加载用户列表失败:', error);
      message.error('加载用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载用户统计
  const loadStats = async () => {
    try {
      // 计算统计信息
      const totalUsers = users.length;
      const activeUsers = users.filter(u => u.status === 'active').length;
      const adminUsers = users.filter(u => u.role === 'admin').length;
      const totalTokens = users.reduce((sum, u) => sum + u.tokens, 0);
      const avgTokens = totalUsers > 0 ? totalTokens / totalUsers : 0;

      setStats({
        total_users: totalUsers,
        active_users: activeUsers,
        admin_users: adminUsers,
        total_tokens: totalTokens,
        avg_tokens: avgTokens,
      });
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  useEffect(() => {
    if (users.length > 0) {
      loadStats();
    }
  }, [users]);

  // 检查用户名可用性
  const checkUsernameAvailability = async (username: string) => {
    if (!username || username.length < 3) {
      setUsernameCheckStatus('');
      setUsernameCheckMessage('');
      return;
    }

    setUsernameCheckStatus('validating');
    setUsernameCheckMessage('检查中...');

    try {
      const result = await userAPI.checkUsernameAvailability(username);
      if (result.available) {
        setUsernameCheckStatus('success');
        setUsernameCheckMessage('用户名可用');
      } else {
        setUsernameCheckStatus('error');
        setUsernameCheckMessage(result.message || '用户名不可用');
      }
    } catch (error: any) {
      setUsernameCheckStatus('error');
      setUsernameCheckMessage('检查失败，请重试');
    }
  };

  // 创建用户
  const handleCreateUser = async (values: any) => {
    try {
      await userAPI.createUser(values);
      message.success('用户创建成功');
      setCreateModalVisible(false);
      createForm.resetFields();
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error: any) {
      console.error('创建用户失败:', error);
      if (error.response && error.response.status === 409) {
        message.error(error.response.data?.message || '用户名已存在，请更换用户名');
      } else {
        message.error(error.response?.data?.message || '创建用户失败');
      }
    }
  };

  // 充值令牌
  const handleRecharge = async (values: any) => {
    if (!selectedUser) return;

    try {
      await userAPI.rechargeTokens(selectedUser.id, values.amount, values.description);
      message.success('充值成功');
      setRechargeModalVisible(false);
      rechargeForm.resetFields();
      setSelectedUser(null);
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error: any) {
      console.error('充值失败:', error);
      message.error(error.response?.data?.message || '充值失败');
    }
  };

  // 更新用户状态
  const handleUpdateStatus = async (userId: string, status: 'active' | 'inactive' | 'banned') => {
    try {
      await userAPI.updateUserStatus(userId, status);
      message.success('用户状态更新成功');
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error: any) {
      console.error('更新用户状态失败:', error);
      message.error(error.response?.data?.message || '更新用户状态失败');
    }
  };

  // 表格列定义
  const columns: ColumnsType<User> = [
    {
      title: '用户ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      render: (id: string) => (
        <Text code>{id}</Text>
      ),
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 150,
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 100,
      render: (role: string) => (
        <Tag color={role === 'admin' ? 'red' : 'blue'}>
          {role === 'admin' ? '管理员' : '普通用户'}
        </Tag>
      ),
    },
    {
      title: '令牌余额',
      dataIndex: 'tokens',
      key: 'tokens',
      width: 120,
      render: (tokens: number) => (
        <Badge count={tokens} showZero color="green" />
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusConfig = {
          active: { color: 'success', text: '正常' },
          inactive: { color: 'warning', text: '停用' },
          banned: { color: 'error', text: '封禁' },
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date: string) => new Date(date).toLocaleString('zh-CN'),
    },
    {
      title: '最后登录',
      dataIndex: 'last_login',
      key: 'last_login',
      width: 180,
      render: (date: string) => date ? new Date(date).toLocaleString('zh-CN') : '从未登录',
    },
    {
      title: '操作',
      key: 'actions',
      width: 250,
      render: (_, record: User) => (
        <Space size="small">
          <Tooltip title="查看令牌记录">
            <Button
              size="small"
              icon={<HistoryOutlined />}
              onClick={() => {
                setSelectedUser(record);
                setTokenUsageModalVisible(true);
              }}
            />
          </Tooltip>

          <Tooltip title="充值令牌">
            <Button
              type="primary"
              size="small"
              icon={<DollarOutlined />}
              onClick={() => {
                setSelectedUser(record);
                setRechargeModalVisible(true);
              }}
            />
          </Tooltip>
          
          {record.status === 'active' ? (
            <Popconfirm
              title="确定要停用此用户吗？"
              onConfirm={() => handleUpdateStatus(record.id, 'inactive')}
            >
              <Tooltip title="停用用户">
                <Button size="small" icon={<StopOutlined />} />
              </Tooltip>
            </Popconfirm>
          ) : (
            <Popconfirm
              title="确定要激活此用户吗？"
              onConfirm={() => handleUpdateStatus(record.id, 'active')}
            >
              <Tooltip title="激活用户">
                <Button size="small" icon={<CheckCircleOutlined />} type="primary" />
              </Tooltip>
            </Popconfirm>
          )}
          
          <Popconfirm
            title="确定要封禁此用户吗？"
            onConfirm={() => handleUpdateStatus(record.id, 'banned')}
          >
            <Tooltip title="封禁用户">
              <Button size="small" danger icon={<StopOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>用户管理</Title>
      
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={stats?.total_users || 0}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={stats?.active_users || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="管理员"
              value={stats?.admin_users || 0}
              prefix={<EditOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总令牌数"
              value={stats?.total_tokens || 0}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作按钮 */}
      <div className="mb-4 flex justify-between">
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建用户
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => loadUsers(pagination.current, pagination.pageSize)}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 用户表格 */}
      <Table
        columns={columns}
        dataSource={users}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }}
        onChange={(paginationInfo) => {
          loadUsers(paginationInfo.current!, paginationInfo.pageSize!);
        }}
      />

      {/* 创建用户模态框 */}
      <Modal
        title="创建新用户"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateUser}
        >
          <Form.Item
            label="用户名"
            name="username"
            validateStatus={usernameCheckStatus}
            help={usernameCheckMessage}
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 2, message: '用户名至少2个字符' },
              { max: 20, message: '用户名最多20个字符' },
            ]}
          >
            <Input
              placeholder="请输入用户名"
              onChange={(e) => {
                const value = e.target.value;
                if (value.length >= 2) {
                  // 防抖处理
                  setTimeout(() => {
                    if (createForm.getFieldValue('username') === value) {
                      checkUsernameAvailability(value);
                    }
                  }, 500);
                } else {
                  setUsernameCheckStatus('');
                  setUsernameCheckMessage('');
                }
              }}
            />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' },
            ]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>

          <Form.Item
            label="初始令牌数"
            name="tokens"
            initialValue={1000}
            rules={[
              { required: true, message: '请输入初始令牌数' },
              { type: 'number', min: 0, message: '令牌数不能为负数' },
            ]}
          >
            <InputNumber
              placeholder="请输入初始令牌数"
              style={{ width: '100%' }}
              min={0}
              max={999999}
            />
          </Form.Item>

          <Form.Item className="mb-0 text-right">
            <Space>
              <Button onClick={() => {
                setCreateModalVisible(false);
                createForm.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 充值令牌模态框 */}
      <Modal
        title={`为用户 ${selectedUser?.username} 充值令牌`}
        open={rechargeModalVisible}
        onCancel={() => {
          setRechargeModalVisible(false);
          rechargeForm.resetFields();
          setSelectedUser(null);
        }}
        footer={null}
        width={500}
      >
        <div className="mb-4">
          <Text type="secondary">
            当前余额: <Text strong>{selectedUser?.tokens || 0}</Text> 令牌
          </Text>
        </div>

        <Form
          form={rechargeForm}
          layout="vertical"
          onFinish={handleRecharge}
        >
          <Form.Item
            label="充值数量"
            name="amount"
            rules={[
              { required: true, message: '请输入充值数量' },
              { type: 'number', min: 1, message: '充值数量至少为1' },
            ]}
          >
            <InputNumber
              placeholder="请输入充值数量"
              style={{ width: '100%' }}
              min={1}
              max={999999}
            />
          </Form.Item>

          <Form.Item
            label="充值说明"
            name="description"
            initialValue="管理员充值"
          >
            <Input.TextArea
              placeholder="请输入充值说明（可选）"
              rows={3}
            />
          </Form.Item>

          <Form.Item className="mb-0 text-right">
            <Space>
              <Button onClick={() => {
                setRechargeModalVisible(false);
                rechargeForm.resetFields();
                setSelectedUser(null);
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确认充值
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 令牌使用记录模态框 */}
      <TokenUsageModal
        visible={tokenUsageModalVisible}
        onClose={() => {
          setTokenUsageModalVisible(false);
          setSelectedUser(null);
        }}
        userId={selectedUser?.id || ''}
        username={selectedUser?.username || ''}
      />
    </div>
  );
}
