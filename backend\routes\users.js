const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// 公开路由
router.post('/login', userController.login);

// 需要认证的路由
router.get('/profile', authenticateToken, userController.getProfile);

// 管理员路由
router.post('/', authenticateToken, requireAdmin, userController.createUser);
router.get('/', authenticateToken, requireAdmin, userController.getAllUsers);
router.post('/:userId/recharge', authenticateToken, requireAdmin, userController.rechargeTokens);
router.patch('/:userId/status', authenticateToken, requireAdmin, userController.updateUserStatus);

module.exports = router;
