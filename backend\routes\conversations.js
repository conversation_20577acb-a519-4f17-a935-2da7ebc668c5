const express = require('express');
const router = express.Router();
const conversationController = require('../controllers/conversationController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// 用户路由
router.post('/', authenticateToken, conversationController.createConversation);
router.get('/my', authenticateToken, conversationController.getUserConversations);
router.get('/:conversationId', authenticateToken, conversationController.getConversation);
router.patch('/:conversationId/title', authenticateToken, conversationController.updateConversationTitle);
router.delete('/:conversationId', authenticateToken, conversationController.deleteConversation);

// 管理员路由
router.get('/', authenticateToken, requireAdmin, conversationController.getAllConversations);

module.exports = router;
