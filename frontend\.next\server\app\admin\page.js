/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/page";
exports.ids = ["app/admin/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/page.tsx */ \"(rsc)/./src/app/admin/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/page\",\n        pathname: \"/admin\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhZG1pbiUyRnBhZ2UmcGFnZT0lMkZhZG1pbiUyRnBhZ2UmYXBwUGF0aHM9JTJGYWRtaW4lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYWRtaW4lMkZwYWdlLnRzeCZhcHBEaXI9RCUzQSU1QyVFOSVBMSVCOSVFNyU5QiVBRSVFNiU5NiU4NyVFNCVCQiVCNiVFNSVBNCVCOSU1Q1hBUCU1Q2Zyb250ZW5kJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDJUU5JUExJUI5JUU3JTlCJUFFJUU2JTk2JTg3JUU0JUJCJUI2JUU1JUE0JUI5JTVDWEFQJTVDZnJvbnRlbmQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHNCQUFzQixvSkFBbUY7QUFDekcsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQixnT0FBbUY7QUFDekcsb0JBQW9CLDRKQUF3RjtBQUcxRztBQUdBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBMk87QUFDL1E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLHNmQUEyTztBQUMvUTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUdyQjtBQUNGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUdFO0FBQ0Y7QUFDTyx3QkFBd0IsdUdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1vZHVsZTAgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXOmhueebruaWh+S7tuWkuVxcXFxYQVBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpO1xuY29uc3QgbW9kdWxlMSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTIgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIik7XG5jb25zdCBtb2R1bGUzID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCIpO1xuY29uc3QgcGFnZTQgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXOmhueebruaWh+S7tuWkuVxcXFxYQVBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxhZG1pblxcXFxwYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnYWRtaW4nLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbcGFnZTQsIFwiRDpcXFxc6aG555uu5paH5Lu25aS5XFxcXFhBUFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGFkbWluXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICBpY29uOiBbKGFzeW5jIChwcm9wcykgPT4gKGF3YWl0IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXI/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIUQ6XFxcXOmhueebruaWh+S7tuWkuVxcXFxYQVBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFttb2R1bGUwLCBcIkQ6XFxcXOmhueebruaWh+S7tuWkuVxcXFxYQVBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFttb2R1bGUxLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4nZm9yYmlkZGVuJzogW21vZHVsZTIsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiXSxcbid1bmF1dGhvcml6ZWQnOiBbbW9kdWxlMywgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCJdLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhRDpcXFxc6aG555uu5paH5Lu25aS5XFxcXFhBUFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiRDpcXFxc6aG555uu5paH5Lu25aS5XFxcXFhBUFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGFkbWluXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9hZG1pbi9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hZG1pblwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJycsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/page.tsx */ \"(rsc)/./src/app/admin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUMlRTklQTElQjklRTclOUIlQUUlRTYlOTYlODclRTQlQkIlQjYlRTUlQTQlQjklNUMlNUNYQVAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUF3RiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxc6aG555uu5paH5Lu25aS5XFxcXFhBUFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGFkbWluXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DConfigProvider%3AD%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cantd%5C%5Ces%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22ConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cnode_modules%5C%5C%40ant-design%5C%5Cnextjs-registry%5C%5Ces%5C%5CAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DConfigProvider%3AD%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cantd%5C%5Ces%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22ConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cnode_modules%5C%5C%40ant-design%5C%5Cnextjs-registry%5C%5Ces%5C%5CAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js */ \"(rsc)/__barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js */ \"(rsc)/../node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DConfigProvider%3AD%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cantd%5C%5Ces%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22ConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cnode_modules%5C%5C%40ant-design%5C%5Cnextjs-registry%5C%5Ces%5C%5CAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm67mlofku7blpLlcXFhBUFxcZnJvbnRlbmRcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\项目文件夹\\XAP\\frontend\\src\\app\\admin\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxc6aG555uu5paH5Lu25aS5XFxYQVBcXGZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MTljYjBmYzNmNjNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ant-design/nextjs-registry */ \"(rsc)/../node_modules/@ant-design/nextjs-registry/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ConfigProvider!=!antd */ \"(rsc)/__barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(rsc)/./node_modules/antd/lib/locale/zh_CN.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"AI学习和咨询系统\",\n    description: \"专为监狱服刑人员设计的AI学习和咨询系统\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_2__.AntdRegistry, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_5__.ConfigProvider, {\n                    locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\项目文件夹\\XAP\\frontend\\src\\contexts\\AuthContext.tsx",
"useAuth",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\项目文件夹\\XAP\\frontend\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(rsc)/__barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js":
/*!**********************************************************************************!*\
  !*** __barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConfigProvider: () => (/* binding */ ConfigProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ConfigProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ConfigProvider() from the server but ConfigProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\项目文件夹\\XAP\\frontend\\node_modules\\antd\\es\\index.js@__barrel_optimize__?names=ConfigProvider",
"ConfigProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/page.tsx */ \"(ssr)/./src/app/admin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUMlRTklQTElQjklRTclOUIlQUUlRTYlOTYlODclRTQlQkIlQjYlRTUlQTQlQjklNUMlNUNYQVAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUF3RiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxc6aG555uu5paH5Lu25aS5XFxcXFhBUFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGFkbWluXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DConfigProvider%3AD%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cantd%5C%5Ces%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22ConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cnode_modules%5C%5C%40ant-design%5C%5Cnextjs-registry%5C%5Ces%5C%5CAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DConfigProvider%3AD%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cantd%5C%5Ces%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22ConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cnode_modules%5C%5C%40ant-design%5C%5Cnextjs-registry%5C%5Ces%5C%5CAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js */ \"(ssr)/__barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js */ \"(ssr)/../node_modules/@ant-design/nextjs-registry/es/AntdRegistry.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DConfigProvider%3AD%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cantd%5C%5Ces%5C%5Cindex.js%22%2C%22ids%22%3A%5B%22ConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5C%5CXAP%5C%5Cnode_modules%5C%5C%40ant-design%5C%5Cnextjs-registry%5C%5Ces%5C%5CAntdRegistry.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Dropdown,Layout,Menu,Row,Space,Statistic,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Dropdown,Layout,Menu,Row,Space,Statistic,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Dropdown,Layout,Menu,Row,Space,Statistic,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Dropdown,Layout,Menu,Row,Space,Statistic,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Dropdown,Layout,Menu,Row,Space,Statistic,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Dropdown,Layout,Menu,Row,Space,Statistic,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Dropdown,Layout,Menu,Row,Space,Statistic,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Dropdown,Layout,Menu,Row,Space,Statistic,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/menu/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Dropdown,Layout,Menu,Row,Space,Statistic,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/dropdown/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Dropdown,Layout,Menu,Row,Space,Statistic,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Dropdown,Layout,Menu,Row,Space,Statistic,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,DashboardOutlined,LogoutOutlined,MessageOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LogoutOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,DashboardOutlined,LogoutOutlined,MessageOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,DashboardOutlined,LogoutOutlined,MessageOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/TeamOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,DashboardOutlined,LogoutOutlined,MessageOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/MessageOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,DashboardOutlined,LogoutOutlined,MessageOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,DashboardOutlined,LogoutOutlined,MessageOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,DashboardOutlined,LogoutOutlined,MessageOutlined,SettingOutlined,TeamOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _components_admin_UserManagement__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/admin/UserManagement */ \"(ssr)/./src/components/admin/UserManagement.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst { Header, Sider, Content } = _barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst { Title, Text } = _barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\nfunction AdminPage() {\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [selectedMenu, setSelectedMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 加载系统统计信息\n    const loadStats = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.systemAPI.getStats();\n            setStats(response.stats);\n        } catch (error) {\n            console.error('加载统计信息失败:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 检查管理员权限\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            if (user && user.role !== 'admin') {\n                router.push('/chat');\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        user,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            if (user?.role === 'admin') {\n                loadStats();\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        user\n    ]);\n    // 用户菜单\n    const userMenuItems = [\n        {\n            key: 'logout',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 13\n            }, this),\n            label: '退出登录',\n            onClick: logout\n        }\n    ];\n    // 侧边栏菜单\n    const sideMenuItems = [\n        {\n            key: 'dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 13\n            }, this),\n            label: '仪表板'\n        },\n        {\n            key: 'users',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 88,\n                columnNumber: 13\n            }, this),\n            label: '用户管理'\n        },\n        {\n            key: 'conversations',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 13\n            }, this),\n            label: '对话管理'\n        },\n        {\n            key: 'stats',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 13\n            }, this),\n            label: '统计分析'\n        },\n        {\n            key: 'system',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 13\n            }, this),\n            label: '系统设置'\n        }\n    ];\n    if (!user || user.role !== 'admin') {\n        return null;\n    }\n    const renderContent = ()=>{\n        switch(selectedMenu){\n            case 'dashboard':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                            level: 2,\n                            children: \"系统概览\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            gutter: [\n                                16,\n                                16\n                            ],\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    xs: 24,\n                                    sm: 12,\n                                    lg: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            title: \"总用户数\",\n                                            value: stats?.users?.total_users || 0,\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 29\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    xs: 24,\n                                    sm: 12,\n                                    lg: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            title: \"活跃用户\",\n                                            value: stats?.users?.active_users || 0,\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 29\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    xs: 24,\n                                    sm: 12,\n                                    lg: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            title: \"总对话数\",\n                                            value: stats?.conversations?.total_conversations || 0,\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 29\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    xs: 24,\n                                    sm: 12,\n                                    lg: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            title: \"总令牌数\",\n                                            value: stats?.users?.total_tokens || 0,\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 29\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            gutter: [\n                                16,\n                                16\n                            ],\n                            className: \"mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    xs: 24,\n                                    lg: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        title: \"用户统计\",\n                                        loading: loading,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            direction: \"vertical\",\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            children: \"管理员用户:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            strong: true,\n                                                            children: stats?.users?.admin_users || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            children: \"普通用户:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            strong: true,\n                                                            children: (stats?.users?.total_users || 0) - (stats?.users?.admin_users || 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            children: \"平均令牌:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            strong: true,\n                                                            children: Math.round(stats?.users?.avg_tokens || 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    xs: 24,\n                                    lg: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        title: \"对话统计\",\n                                        loading: loading,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            direction: \"vertical\",\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            children: \"活跃对话:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            strong: true,\n                                                            children: stats?.conversations?.active_conversations || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            children: \"平均消息数:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            strong: true,\n                                                            children: Math.round(stats?.conversations?.avg_messages_per_conversation || 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 11\n                }, this);\n            case 'users':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_UserManagement__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 16\n                }, this);\n            case 'conversations':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                            level: 2,\n                            children: \"对话管理\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            type: \"secondary\",\n                            children: \"对话管理功能开发中...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 11\n                }, this);\n            case 'stats':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                            level: 2,\n                            children: \"统计分析\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            type: \"secondary\",\n                            children: \"统计分析功能开发中...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, this);\n            case 'system':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                            level: 2,\n                            children: \"系统设置\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            type: \"secondary\",\n                            children: \"系统设置功能开发中...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sider, {\n                width: 240,\n                className: \"bg-white border-r\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                            level: 4,\n                            className: \"m-0 text-center\",\n                            children: \"管理后台\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        mode: \"inline\",\n                        selectedKeys: [\n                            selectedMenu\n                        ],\n                        items: sideMenuItems,\n                        className: \"border-none\",\n                        onSelect: ({ key })=>setSelectedMenu(key)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                        className: \"bg-white border-b px-6 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                level: 4,\n                                className: \"m-0\",\n                                children: \"AI学习和咨询系统 - 管理后台\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        type: \"secondary\",\n                                        children: [\n                                            \"管理员：\",\n                                            user.username\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        menu: {\n                                            items: userMenuItems\n                                        },\n                                        placement: \"bottomRight\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            type: \"text\",\n                                            className: \"flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Dropdown_Layout_Menu_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_DashboardOutlined_LogoutOutlined_MessageOutlined_SettingOutlined_TeamOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 31\n                                                }, void 0),\n                                                size: \"small\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                        className: \"p-6\",\n                        children: renderContent()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2FkbWluL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFhOUI7QUFTYTtBQUNzQjtBQUNMO0FBQ047QUFDeUI7QUFFL0QsTUFBTSxFQUFFd0IsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLE9BQU8sRUFBRSxHQUFHeEIsOElBQU1BO0FBQ3pDLE1BQU0sRUFBRXlCLEtBQUssRUFBRUMsSUFBSSxFQUFFLEdBQUd4Qiw4SUFBVUE7QUFJbkIsU0FBU3lCO0lBQ3RCLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxNQUFNLEVBQUUsR0FBR1gsOERBQU9BO0lBQ2hDLE1BQU1ZLFNBQVNYLDBEQUFTQTtJQUN4QixNQUFNLENBQUNZLGNBQWNDLGdCQUFnQixHQUFHbEMsK0NBQVFBLENBQVU7SUFDMUQsTUFBTSxDQUFDbUMsT0FBT0MsU0FBUyxHQUFHcEMsK0NBQVFBLENBQU07SUFDeEMsTUFBTSxDQUFDcUMsU0FBU0MsV0FBVyxHQUFHdEMsK0NBQVFBLENBQUM7SUFFdkMsV0FBVztJQUNYLE1BQU11QyxZQUFZO1FBQ2hCLElBQUk7WUFDRkQsV0FBVztZQUNYLE1BQU1FLFdBQVcsTUFBTWxCLCtDQUFTQSxDQUFDbUIsUUFBUTtZQUN6Q0wsU0FBU0ksU0FBU0wsS0FBSztRQUN6QixFQUFFLE9BQU9PLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGFBQWFBO1FBQzdCLFNBQVU7WUFDUkosV0FBVztRQUNiO0lBQ0Y7SUFFQSxVQUFVO0lBQ1ZyQyxnREFBU0E7K0JBQUM7WUFDUixJQUFJNkIsUUFBUUEsS0FBS2MsSUFBSSxLQUFLLFNBQVM7Z0JBQ2pDWixPQUFPYSxJQUFJLENBQUM7WUFDZDtRQUNGOzhCQUFHO1FBQUNmO1FBQU1FO0tBQU87SUFFakIvQixnREFBU0E7K0JBQUM7WUFDUixJQUFJNkIsTUFBTWMsU0FBUyxTQUFTO2dCQUMxQkw7WUFDRjtRQUNGOzhCQUFHO1FBQUNUO0tBQUs7SUFFVCxPQUFPO0lBQ1AsTUFBTWdCLGdCQUFnQjtRQUNwQjtZQUNFQyxLQUFLO1lBQ0xDLG9CQUFNLDhEQUFDbEMsMkxBQWNBOzs7OztZQUNyQm1DLE9BQU87WUFDUEMsU0FBU25CO1FBQ1g7S0FDRDtJQUVELFFBQVE7SUFDUixNQUFNb0IsZ0JBQWdCO1FBQ3BCO1lBQ0VKLEtBQUs7WUFDTEMsb0JBQU0sOERBQUNqQywyTEFBaUJBOzs7OztZQUN4QmtDLE9BQU87UUFDVDtRQUNBO1lBQ0VGLEtBQUs7WUFDTEMsb0JBQU0sOERBQUNoQyw0TEFBWUE7Ozs7O1lBQ25CaUMsT0FBTztRQUNUO1FBQ0E7WUFDRUYsS0FBSztZQUNMQyxvQkFBTSw4REFBQy9CLDRMQUFlQTs7Ozs7WUFDdEJnQyxPQUFPO1FBQ1Q7UUFDQTtZQUNFRixLQUFLO1lBQ0xDLG9CQUFNLDhEQUFDN0IsNExBQWdCQTs7Ozs7WUFDdkI4QixPQUFPO1FBQ1Q7UUFDQTtZQUNFRixLQUFLO1lBQ0xDLG9CQUFNLDhEQUFDOUIsNExBQWVBOzs7OztZQUN0QitCLE9BQU87UUFDVDtLQUNEO0lBRUQsSUFBSSxDQUFDbkIsUUFBUUEsS0FBS2MsSUFBSSxLQUFLLFNBQVM7UUFDbEMsT0FBTztJQUNUO0lBRUEsTUFBTVEsZ0JBQWdCO1FBQ3BCLE9BQVFuQjtZQUNOLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNvQjs7c0NBQ0MsOERBQUMxQjs0QkFBTTJCLE9BQU87c0NBQUc7Ozs7OztzQ0FDakIsOERBQUMzQywrSUFBR0E7NEJBQUM0QyxRQUFRO2dDQUFDO2dDQUFJOzZCQUFHOzs4Q0FDbkIsOERBQUMzQywrSUFBR0E7b0NBQUM0QyxJQUFJO29DQUFJQyxJQUFJO29DQUFJQyxJQUFJOzhDQUN2Qiw0RUFBQ2pELCtJQUFJQTtrREFDSCw0RUFBQ0MsK0lBQVNBOzRDQUNSaUQsT0FBTTs0Q0FDTkMsT0FBT3pCLE9BQU8wQixPQUFPQyxlQUFlOzRDQUNwQ0Msc0JBQVEsOERBQUMvQyw0TEFBWUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUkzQiw4REFBQ0osK0lBQUdBO29DQUFDNEMsSUFBSTtvQ0FBSUMsSUFBSTtvQ0FBSUMsSUFBSTs4Q0FDdkIsNEVBQUNqRCwrSUFBSUE7a0RBQ0gsNEVBQUNDLCtJQUFTQTs0Q0FDUmlELE9BQU07NENBQ05DLE9BQU96QixPQUFPMEIsT0FBT0csZ0JBQWdCOzRDQUNyQ0Qsc0JBQVEsOERBQUNsRCw0TEFBWUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUkzQiw4REFBQ0QsK0lBQUdBO29DQUFDNEMsSUFBSTtvQ0FBSUMsSUFBSTtvQ0FBSUMsSUFBSTs4Q0FDdkIsNEVBQUNqRCwrSUFBSUE7a0RBQ0gsNEVBQUNDLCtJQUFTQTs0Q0FDUmlELE9BQU07NENBQ05DLE9BQU96QixPQUFPOEIsZUFBZUMsdUJBQXVCOzRDQUNwREgsc0JBQVEsOERBQUM5Qyw0TEFBZUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUk5Qiw4REFBQ0wsK0lBQUdBO29DQUFDNEMsSUFBSTtvQ0FBSUMsSUFBSTtvQ0FBSUMsSUFBSTs4Q0FDdkIsNEVBQUNqRCwrSUFBSUE7a0RBQ0gsNEVBQUNDLCtJQUFTQTs0Q0FDUmlELE9BQU07NENBQ05DLE9BQU96QixPQUFPMEIsT0FBT00sZ0JBQWdCOzRDQUNyQ0osc0JBQVEsOERBQUM1Qyw0TEFBZ0JBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNakMsOERBQUNSLCtJQUFHQTs0QkFBQzRDLFFBQVE7Z0NBQUM7Z0NBQUk7NkJBQUc7NEJBQUVhLFdBQVU7OzhDQUMvQiw4REFBQ3hELCtJQUFHQTtvQ0FBQzRDLElBQUk7b0NBQUlFLElBQUk7OENBQ2YsNEVBQUNqRCwrSUFBSUE7d0NBQUNrRCxPQUFNO3dDQUFPdEIsU0FBU0E7a0RBQzFCLDRFQUFDaEMsK0lBQUtBOzRDQUFDZ0UsV0FBVTs0Q0FBV0QsV0FBVTs7OERBQ3BDLDhEQUFDZjtvREFBSWUsV0FBVTs7c0VBQ2IsOERBQUN4QztzRUFBSzs7Ozs7O3NFQUNOLDhEQUFDQTs0REFBSzBDLE1BQU07c0VBQUVuQyxPQUFPMEIsT0FBT1UsZUFBZTs7Ozs7Ozs7Ozs7OzhEQUU3Qyw4REFBQ2xCO29EQUFJZSxXQUFVOztzRUFDYiw4REFBQ3hDO3NFQUFLOzs7Ozs7c0VBQ04sOERBQUNBOzREQUFLMEMsTUFBTTtzRUFBRSxDQUFDbkMsT0FBTzBCLE9BQU9DLGVBQWUsS0FBTTNCLENBQUFBLE9BQU8wQixPQUFPVSxlQUFlOzs7Ozs7Ozs7Ozs7OERBRWpGLDhEQUFDbEI7b0RBQUllLFdBQVU7O3NFQUNiLDhEQUFDeEM7c0VBQUs7Ozs7OztzRUFDTiw4REFBQ0E7NERBQUswQyxNQUFNO3NFQUFFRSxLQUFLQyxLQUFLLENBQUN0QyxPQUFPMEIsT0FBT2EsY0FBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLN0QsOERBQUM5RCwrSUFBR0E7b0NBQUM0QyxJQUFJO29DQUFJRSxJQUFJOzhDQUNmLDRFQUFDakQsK0lBQUlBO3dDQUFDa0QsT0FBTTt3Q0FBT3RCLFNBQVNBO2tEQUMxQiw0RUFBQ2hDLCtJQUFLQTs0Q0FBQ2dFLFdBQVU7NENBQVdELFdBQVU7OzhEQUNwQyw4REFBQ2Y7b0RBQUllLFdBQVU7O3NFQUNiLDhEQUFDeEM7c0VBQUs7Ozs7OztzRUFDTiw4REFBQ0E7NERBQUswQyxNQUFNO3NFQUFFbkMsT0FBTzhCLGVBQWVVLHdCQUF3Qjs7Ozs7Ozs7Ozs7OzhEQUU5RCw4REFBQ3RCO29EQUFJZSxXQUFVOztzRUFDYiw4REFBQ3hDO3NFQUFLOzs7Ozs7c0VBQ04sOERBQUNBOzREQUFLMEMsTUFBTTtzRUFBRUUsS0FBS0MsS0FBSyxDQUFDdEMsT0FBTzhCLGVBQWVXLGlDQUFpQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVFoRyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDckQsd0VBQWNBOzs7OztZQUN4QixLQUFLO2dCQUNILHFCQUNFLDhEQUFDOEI7O3NDQUNDLDhEQUFDMUI7NEJBQU0yQixPQUFPO3NDQUFHOzs7Ozs7c0NBQ2pCLDhEQUFDMUI7NEJBQUtpRCxNQUFLO3NDQUFZOzs7Ozs7Ozs7Ozs7WUFHN0IsS0FBSztnQkFDSCxxQkFDRSw4REFBQ3hCOztzQ0FDQyw4REFBQzFCOzRCQUFNMkIsT0FBTztzQ0FBRzs7Ozs7O3NDQUNqQiw4REFBQzFCOzRCQUFLaUQsTUFBSztzQ0FBWTs7Ozs7Ozs7Ozs7O1lBRzdCLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUN4Qjs7c0NBQ0MsOERBQUMxQjs0QkFBTTJCLE9BQU87c0NBQUc7Ozs7OztzQ0FDakIsOERBQUMxQjs0QkFBS2lELE1BQUs7c0NBQVk7Ozs7Ozs7Ozs7OztZQUc3QjtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDM0UsOElBQU1BO1FBQUNrRSxXQUFVOzswQkFFaEIsOERBQUMzQztnQkFBTXFELE9BQU87Z0JBQUtWLFdBQVU7O2tDQUMzQiw4REFBQ2Y7d0JBQUllLFdBQVU7a0NBQ2IsNEVBQUN6Qzs0QkFBTTJCLE9BQU87NEJBQUdjLFdBQVU7c0NBQWtCOzs7Ozs7Ozs7OztrQ0FLL0MsOERBQUNqRSwrSUFBSUE7d0JBQ0g0RSxNQUFLO3dCQUNMQyxjQUFjOzRCQUFDL0M7eUJBQWE7d0JBQzVCZ0QsT0FBTzlCO3dCQUNQaUIsV0FBVTt3QkFDVmMsVUFBVSxDQUFDLEVBQUVuQyxHQUFHLEVBQUUsR0FBS2IsZ0JBQWdCYTs7Ozs7Ozs7Ozs7OzBCQUszQyw4REFBQzdDLDhJQUFNQTs7a0NBRUwsOERBQUNzQjt3QkFBTzRDLFdBQVU7OzBDQUNoQiw4REFBQ3pDO2dDQUFNMkIsT0FBTztnQ0FBR2MsV0FBVTswQ0FBTTs7Ozs7OzBDQUlqQyw4REFBQy9ELCtJQUFLQTs7a0RBQ0osOERBQUN1Qjt3Q0FBS2lELE1BQUs7OzRDQUFZOzRDQUNoQi9DLEtBQUtxRCxRQUFROzs7Ozs7O2tEQUVwQiw4REFBQzVFLCtJQUFRQTt3Q0FDUDZFLE1BQU07NENBQUVILE9BQU9uQzt3Q0FBYzt3Q0FDN0J1QyxXQUFVO2tEQUVWLDRFQUFDN0UsK0lBQU1BOzRDQUFDcUUsTUFBSzs0Q0FBT1QsV0FBVTtzREFDNUIsNEVBQUM5RCwrSUFBTUE7Z0RBQUMwQyxvQkFBTSw4REFBQ25DLDRMQUFZQTs7Ozs7Z0RBQUt5RSxNQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU83Qyw4REFBQzVEO3dCQUFRMEMsV0FBVTtrQ0FDaEJoQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkQ6XFzpobnnm67mlofku7blpLlcXFhBUFxcZnJvbnRlbmRcXHNyY1xcYXBwXFxhZG1pblxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgXG4gIExheW91dCwgXG4gIE1lbnUsIFxuICBUeXBvZ3JhcGh5LCBcbiAgU3BhY2UsIFxuICBBdmF0YXIsIFxuICBEcm9wZG93biwgXG4gIEJ1dHRvbixcbiAgQ2FyZCxcbiAgU3RhdGlzdGljLFxuICBSb3csXG4gIENvbFxufSBmcm9tICdhbnRkJztcbmltcG9ydCB7IFxuICBVc2VyT3V0bGluZWQsIFxuICBMb2dvdXRPdXRsaW5lZCxcbiAgRGFzaGJvYXJkT3V0bGluZWQsXG4gIFRlYW1PdXRsaW5lZCxcbiAgTWVzc2FnZU91dGxpbmVkLFxuICBTZXR0aW5nT3V0bGluZWQsXG4gIEJhckNoYXJ0T3V0bGluZWRcbn0gZnJvbSAnQGFudC1kZXNpZ24vaWNvbnMnO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IHN5c3RlbUFQSSB9IGZyb20gJ0AvbGliL2FwaSc7XG5pbXBvcnQgVXNlck1hbmFnZW1lbnQgZnJvbSAnQC9jb21wb25lbnRzL2FkbWluL1VzZXJNYW5hZ2VtZW50JztcblxuY29uc3QgeyBIZWFkZXIsIFNpZGVyLCBDb250ZW50IH0gPSBMYXlvdXQ7XG5jb25zdCB7IFRpdGxlLCBUZXh0IH0gPSBUeXBvZ3JhcGh5O1xuXG50eXBlIE1lbnVLZXkgPSAnZGFzaGJvYXJkJyB8ICd1c2VycycgfCAnY29udmVyc2F0aW9ucycgfCAnc3lzdGVtJyB8ICdzdGF0cyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFkbWluUGFnZSgpIHtcbiAgY29uc3QgeyB1c2VyLCBsb2dvdXQgfSA9IHVzZUF1dGgoKTtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IFtzZWxlY3RlZE1lbnUsIHNldFNlbGVjdGVkTWVudV0gPSB1c2VTdGF0ZTxNZW51S2V5PignZGFzaGJvYXJkJyk7XG4gIGNvbnN0IFtzdGF0cywgc2V0U3RhdHNdID0gdXNlU3RhdGU8YW55PihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIOWKoOi9veezu+e7n+e7n+iuoeS/oeaBr1xuICBjb25zdCBsb2FkU3RhdHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHN5c3RlbUFQSS5nZXRTdGF0cygpO1xuICAgICAgc2V0U3RhdHMocmVzcG9uc2Uuc3RhdHMpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfliqDovb3nu5/orqHkv6Hmga/lpLHotKU6JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5qOA5p+l566h55CG5ZGY5p2D6ZmQXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHVzZXIgJiYgdXNlci5yb2xlICE9PSAnYWRtaW4nKSB7XG4gICAgICByb3V0ZXIucHVzaCgnL2NoYXQnKTtcbiAgICB9XG4gIH0sIFt1c2VyLCByb3V0ZXJdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh1c2VyPy5yb2xlID09PSAnYWRtaW4nKSB7XG4gICAgICBsb2FkU3RhdHMoKTtcbiAgICB9XG4gIH0sIFt1c2VyXSk7XG5cbiAgLy8g55So5oi36I+c5Y2VXG4gIGNvbnN0IHVzZXJNZW51SXRlbXMgPSBbXG4gICAge1xuICAgICAga2V5OiAnbG9nb3V0JyxcbiAgICAgIGljb246IDxMb2dvdXRPdXRsaW5lZCAvPixcbiAgICAgIGxhYmVsOiAn6YCA5Ye655m75b2VJyxcbiAgICAgIG9uQ2xpY2s6IGxvZ291dCxcbiAgICB9LFxuICBdO1xuXG4gIC8vIOS+p+i+ueagj+iPnOWNlVxuICBjb25zdCBzaWRlTWVudUl0ZW1zID0gW1xuICAgIHtcbiAgICAgIGtleTogJ2Rhc2hib2FyZCcsXG4gICAgICBpY29uOiA8RGFzaGJvYXJkT3V0bGluZWQgLz4sXG4gICAgICBsYWJlbDogJ+S7quihqOadvycsXG4gICAgfSxcbiAgICB7XG4gICAgICBrZXk6ICd1c2VycycsXG4gICAgICBpY29uOiA8VGVhbU91dGxpbmVkIC8+LFxuICAgICAgbGFiZWw6ICfnlKjmiLfnrqHnkIYnLFxuICAgIH0sXG4gICAge1xuICAgICAga2V5OiAnY29udmVyc2F0aW9ucycsXG4gICAgICBpY29uOiA8TWVzc2FnZU91dGxpbmVkIC8+LFxuICAgICAgbGFiZWw6ICflr7nor53nrqHnkIYnLFxuICAgIH0sXG4gICAge1xuICAgICAga2V5OiAnc3RhdHMnLFxuICAgICAgaWNvbjogPEJhckNoYXJ0T3V0bGluZWQgLz4sXG4gICAgICBsYWJlbDogJ+e7n+iuoeWIhuaekCcsXG4gICAgfSxcbiAgICB7XG4gICAgICBrZXk6ICdzeXN0ZW0nLFxuICAgICAgaWNvbjogPFNldHRpbmdPdXRsaW5lZCAvPixcbiAgICAgIGxhYmVsOiAn57O757uf6K6+572uJyxcbiAgICB9LFxuICBdO1xuXG4gIGlmICghdXNlciB8fCB1c2VyLnJvbGUgIT09ICdhZG1pbicpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIGNvbnN0IHJlbmRlckNvbnRlbnQgPSAoKSA9PiB7XG4gICAgc3dpdGNoIChzZWxlY3RlZE1lbnUpIHtcbiAgICAgIGNhc2UgJ2Rhc2hib2FyZCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxUaXRsZSBsZXZlbD17Mn0+57O757uf5qaC6KeIPC9UaXRsZT5cbiAgICAgICAgICAgIDxSb3cgZ3V0dGVyPXtbMTYsIDE2XX0+XG4gICAgICAgICAgICAgIDxDb2wgeHM9ezI0fSBzbT17MTJ9IGxnPXs2fT5cbiAgICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICAgIDxTdGF0aXN0aWNcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCLmgLvnlKjmiLfmlbBcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c3RhdHM/LnVzZXJzPy50b3RhbF91c2VycyB8fCAwfVxuICAgICAgICAgICAgICAgICAgICBwcmVmaXg9ezxUZWFtT3V0bGluZWQgLz59XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgPC9Db2w+XG4gICAgICAgICAgICAgIDxDb2wgeHM9ezI0fSBzbT17MTJ9IGxnPXs2fT5cbiAgICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICAgIDxTdGF0aXN0aWNcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCLmtLvot4PnlKjmiLdcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c3RhdHM/LnVzZXJzPy5hY3RpdmVfdXNlcnMgfHwgMH1cbiAgICAgICAgICAgICAgICAgICAgcHJlZml4PXs8VXNlck91dGxpbmVkIC8+fVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgIDwvQ29sPlxuICAgICAgICAgICAgICA8Q29sIHhzPXsyNH0gc209ezEyfSBsZz17Nn0+XG4gICAgICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgICAgICA8U3RhdGlzdGljXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlPVwi5oC75a+56K+d5pWwXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3N0YXRzPy5jb252ZXJzYXRpb25zPy50b3RhbF9jb252ZXJzYXRpb25zIHx8IDB9XG4gICAgICAgICAgICAgICAgICAgIHByZWZpeD17PE1lc3NhZ2VPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICA8L0NvbD5cbiAgICAgICAgICAgICAgPENvbCB4cz17MjR9IHNtPXsxMn0gbGc9ezZ9PlxuICAgICAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICAgICAgPFN0YXRpc3RpY1xuICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIuaAu+S7pOeJjOaVsFwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzdGF0cz8udXNlcnM/LnRvdGFsX3Rva2VucyB8fCAwfVxuICAgICAgICAgICAgICAgICAgICBwcmVmaXg9ezxCYXJDaGFydE91dGxpbmVkIC8+fVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgIDwvQ29sPlxuICAgICAgICAgICAgPC9Sb3c+XG5cbiAgICAgICAgICAgIDxSb3cgZ3V0dGVyPXtbMTYsIDE2XX0gY2xhc3NOYW1lPVwibXQtNlwiPlxuICAgICAgICAgICAgICA8Q29sIHhzPXsyNH0gbGc9ezEyfT5cbiAgICAgICAgICAgICAgICA8Q2FyZCB0aXRsZT1cIueUqOaIt+e7n+iuoVwiIGxvYWRpbmc9e2xvYWRpbmd9PlxuICAgICAgICAgICAgICAgICAgPFNwYWNlIGRpcmVjdGlvbj1cInZlcnRpY2FsXCIgY2xhc3NOYW1lPVwidy1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dD7nrqHnkIblkZjnlKjmiLc6PC9UZXh0PlxuICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IHN0cm9uZz57c3RhdHM/LnVzZXJzPy5hZG1pbl91c2VycyB8fCAwfTwvVGV4dD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dD7mma7pgJrnlKjmiLc6PC9UZXh0PlxuICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IHN0cm9uZz57KHN0YXRzPy51c2Vycz8udG90YWxfdXNlcnMgfHwgMCkgLSAoc3RhdHM/LnVzZXJzPy5hZG1pbl91c2VycyB8fCAwKX08L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFRleHQ+5bmz5Z2H5Luk54mMOjwvVGV4dD5cbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBzdHJvbmc+e01hdGgucm91bmQoc3RhdHM/LnVzZXJzPy5hdmdfdG9rZW5zIHx8IDApfTwvVGV4dD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L1NwYWNlPlxuICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgPC9Db2w+XG4gICAgICAgICAgICAgIDxDb2wgeHM9ezI0fSBsZz17MTJ9PlxuICAgICAgICAgICAgICAgIDxDYXJkIHRpdGxlPVwi5a+56K+d57uf6K6hXCIgbG9hZGluZz17bG9hZGluZ30+XG4gICAgICAgICAgICAgICAgICA8U3BhY2UgZGlyZWN0aW9uPVwidmVydGljYWxcIiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxUZXh0Pua0u+i3g+WvueivnTo8L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgICAgPFRleHQgc3Ryb25nPntzdGF0cz8uY29udmVyc2F0aW9ucz8uYWN0aXZlX2NvbnZlcnNhdGlvbnMgfHwgMH08L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFRleHQ+5bmz5Z2H5raI5oGv5pWwOjwvVGV4dD5cbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBzdHJvbmc+e01hdGgucm91bmQoc3RhdHM/LmNvbnZlcnNhdGlvbnM/LmF2Z19tZXNzYWdlc19wZXJfY29udmVyc2F0aW9uIHx8IDApfTwvVGV4dD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L1NwYWNlPlxuICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgPC9Db2w+XG4gICAgICAgICAgICA8L1Jvdz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ3VzZXJzJzpcbiAgICAgICAgcmV0dXJuIDxVc2VyTWFuYWdlbWVudCAvPjtcbiAgICAgIGNhc2UgJ2NvbnZlcnNhdGlvbnMnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8VGl0bGUgbGV2ZWw9ezJ9PuWvueivneeuoeeQhjwvVGl0bGU+XG4gICAgICAgICAgICA8VGV4dCB0eXBlPVwic2Vjb25kYXJ5XCI+5a+56K+d566h55CG5Yqf6IO95byA5Y+R5LitLi4uPC9UZXh0PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgY2FzZSAnc3RhdHMnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8VGl0bGUgbGV2ZWw9ezJ9Pue7n+iuoeWIhuaekDwvVGl0bGU+XG4gICAgICAgICAgICA8VGV4dCB0eXBlPVwic2Vjb25kYXJ5XCI+57uf6K6h5YiG5p6Q5Yqf6IO95byA5Y+R5LitLi4uPC9UZXh0PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgICAgY2FzZSAnc3lzdGVtJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPFRpdGxlIGxldmVsPXsyfT7ns7vnu5/orr7nva48L1RpdGxlPlxuICAgICAgICAgICAgPFRleHQgdHlwZT1cInNlY29uZGFyeVwiPuezu+e7n+iuvue9ruWKn+iDveW8gOWPkeS4rS4uLjwvVGV4dD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxMYXlvdXQgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuXCI+XG4gICAgICB7Lyog5L6n6L655qCPICovfVxuICAgICAgPFNpZGVyIHdpZHRoPXsyNDB9IGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci1yXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci1iXCI+XG4gICAgICAgICAgPFRpdGxlIGxldmVsPXs0fSBjbGFzc05hbWU9XCJtLTAgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIOeuoeeQhuWQjuWPsFxuICAgICAgICAgIDwvVGl0bGU+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxNZW51XG4gICAgICAgICAgbW9kZT1cImlubGluZVwiXG4gICAgICAgICAgc2VsZWN0ZWRLZXlzPXtbc2VsZWN0ZWRNZW51XX1cbiAgICAgICAgICBpdGVtcz17c2lkZU1lbnVJdGVtc31cbiAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItbm9uZVwiXG4gICAgICAgICAgb25TZWxlY3Q9eyh7IGtleSB9KSA9PiBzZXRTZWxlY3RlZE1lbnUoa2V5IGFzIE1lbnVLZXkpfVxuICAgICAgICAvPlxuICAgICAgPC9TaWRlcj5cblxuICAgICAgey8qIOS4u+WGheWuueWMuiAqL31cbiAgICAgIDxMYXlvdXQ+XG4gICAgICAgIHsvKiDpobbpg6jlr7zoiKogKi99XG4gICAgICAgIDxIZWFkZXIgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgcHgtNiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8VGl0bGUgbGV2ZWw9ezR9IGNsYXNzTmFtZT1cIm0tMFwiPlxuICAgICAgICAgICAgQUnlrabkuaDlkozlkqjor6Lns7vnu58gLSDnrqHnkIblkI7lj7BcbiAgICAgICAgICA8L1RpdGxlPlxuXG4gICAgICAgICAgPFNwYWNlPlxuICAgICAgICAgICAgPFRleHQgdHlwZT1cInNlY29uZGFyeVwiPlxuICAgICAgICAgICAgICDnrqHnkIblkZjvvJp7dXNlci51c2VybmFtZX1cbiAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgIDxEcm9wZG93blxuICAgICAgICAgICAgICBtZW51PXt7IGl0ZW1zOiB1c2VyTWVudUl0ZW1zIH19XG4gICAgICAgICAgICAgIHBsYWNlbWVudD1cImJvdHRvbVJpZ2h0XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEJ1dHRvbiB0eXBlPVwidGV4dFwiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPEF2YXRhciBpY29uPXs8VXNlck91dGxpbmVkIC8+fSBzaXplPVwic21hbGxcIiAvPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvRHJvcGRvd24+XG4gICAgICAgICAgPC9TcGFjZT5cbiAgICAgICAgPC9IZWFkZXI+XG5cbiAgICAgICAgey8qIOWGheWuueWMuiAqL31cbiAgICAgICAgPENvbnRlbnQgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAge3JlbmRlckNvbnRlbnQoKX1cbiAgICAgICAgPC9Db250ZW50PlxuICAgICAgPC9MYXlvdXQ+XG4gICAgPC9MYXlvdXQ+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJMYXlvdXQiLCJNZW51IiwiVHlwb2dyYXBoeSIsIlNwYWNlIiwiQXZhdGFyIiwiRHJvcGRvd24iLCJCdXR0b24iLCJDYXJkIiwiU3RhdGlzdGljIiwiUm93IiwiQ29sIiwiVXNlck91dGxpbmVkIiwiTG9nb3V0T3V0bGluZWQiLCJEYXNoYm9hcmRPdXRsaW5lZCIsIlRlYW1PdXRsaW5lZCIsIk1lc3NhZ2VPdXRsaW5lZCIsIlNldHRpbmdPdXRsaW5lZCIsIkJhckNoYXJ0T3V0bGluZWQiLCJ1c2VBdXRoIiwidXNlUm91dGVyIiwic3lzdGVtQVBJIiwiVXNlck1hbmFnZW1lbnQiLCJIZWFkZXIiLCJTaWRlciIsIkNvbnRlbnQiLCJUaXRsZSIsIlRleHQiLCJBZG1pblBhZ2UiLCJ1c2VyIiwibG9nb3V0Iiwicm91dGVyIiwic2VsZWN0ZWRNZW51Iiwic2V0U2VsZWN0ZWRNZW51Iiwic3RhdHMiLCJzZXRTdGF0cyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwibG9hZFN0YXRzIiwicmVzcG9uc2UiLCJnZXRTdGF0cyIsImVycm9yIiwiY29uc29sZSIsInJvbGUiLCJwdXNoIiwidXNlck1lbnVJdGVtcyIsImtleSIsImljb24iLCJsYWJlbCIsIm9uQ2xpY2siLCJzaWRlTWVudUl0ZW1zIiwicmVuZGVyQ29udGVudCIsImRpdiIsImxldmVsIiwiZ3V0dGVyIiwieHMiLCJzbSIsImxnIiwidGl0bGUiLCJ2YWx1ZSIsInVzZXJzIiwidG90YWxfdXNlcnMiLCJwcmVmaXgiLCJhY3RpdmVfdXNlcnMiLCJjb252ZXJzYXRpb25zIiwidG90YWxfY29udmVyc2F0aW9ucyIsInRvdGFsX3Rva2VucyIsImNsYXNzTmFtZSIsImRpcmVjdGlvbiIsInN0cm9uZyIsImFkbWluX3VzZXJzIiwiTWF0aCIsInJvdW5kIiwiYXZnX3Rva2VucyIsImFjdGl2ZV9jb252ZXJzYXRpb25zIiwiYXZnX21lc3NhZ2VzX3Blcl9jb252ZXJzYXRpb24iLCJ0eXBlIiwid2lkdGgiLCJtb2RlIiwic2VsZWN0ZWRLZXlzIiwiaXRlbXMiLCJvblNlbGVjdCIsInVzZXJuYW1lIiwibWVudSIsInBsYWNlbWVudCIsInNpemUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/admin/TokenUsageModal.tsx":
/*!**************************************************!*\
  !*** ./src/components/admin/TokenUsageModal.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenUsageModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Modal,Select,Space,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Modal,Select,Space,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/date-picker/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Modal,Select,Space,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Modal,Select,Space,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Modal,Select,Space,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Modal,Select,Space,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Modal,Select,Space,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Modal,Select,Space,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,DatePicker,Modal,Select,Space,Table,Tag,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_ReloadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ReloadOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst { Text } = _barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nconst { RangePicker } = _barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Option } = _barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction TokenUsageModal({ visible, onClose, userId, username }) {\n    const [records, setRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 1,\n        pageSize: 20,\n        total: 0\n    });\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        actionType: 'all',\n        dateRange: null\n    });\n    // 加载令牌使用记录\n    const loadTokenUsage = async (page = 1, pageSize = 20)=>{\n        try {\n            setLoading(true);\n            // 构建过滤器参数\n            const filterParams = {\n                actionType: filters.actionType\n            };\n            if (filters.dateRange && filters.dateRange.length === 2) {\n                filterParams.startDate = filters.dateRange[0].format('YYYY-MM-DD');\n                filterParams.endDate = filters.dateRange[1].format('YYYY-MM-DD');\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.getTokenUsage(userId, page, pageSize, filterParams);\n            setRecords(response.records);\n            setPagination({\n                current: page,\n                pageSize,\n                total: response.pagination.total\n            });\n        } catch (error) {\n            console.error('加载令牌使用记录失败:', error);\n            _barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('加载令牌使用记录失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TokenUsageModal.useEffect\": ()=>{\n            if (visible && userId) {\n                loadTokenUsage();\n            }\n        }\n    }[\"TokenUsageModal.useEffect\"], [\n        visible,\n        userId\n    ]);\n    // 当过滤器改变时重新加载数据\n    const handleFilterChange = ()=>{\n        loadTokenUsage(1, pagination.pageSize);\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: '时间',\n            dataIndex: 'created_at',\n            key: 'created_at',\n            width: 180,\n            render: (date)=>new Date(date).toLocaleString('zh-CN')\n        },\n        {\n            title: '操作类型',\n            dataIndex: 'action_type',\n            key: 'action_type',\n            width: 100,\n            render: (type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    color: type === 'recharge' ? 'green' : 'red',\n                    children: type === 'recharge' ? '充值' : '消费'\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\TokenUsageModal.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '数量',\n            dataIndex: 'amount',\n            key: 'amount',\n            width: 100,\n            render: (amount, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                    style: {\n                        color: record.action_type === 'recharge' ? '#52c41a' : '#ff4d4f'\n                    },\n                    children: [\n                        record.action_type === 'recharge' ? '+' : '-',\n                        amount\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\TokenUsageModal.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '变更前余额',\n            dataIndex: 'balance_before',\n            key: 'balance_before',\n            width: 120\n        },\n        {\n            title: '变更后余额',\n            dataIndex: 'balance_after',\n            key: 'balance_after',\n            width: 120\n        },\n        {\n            title: '说明',\n            dataIndex: 'description',\n            key: 'description',\n            ellipsis: true\n        },\n        {\n            title: '操作员',\n            dataIndex: 'admin_id',\n            key: 'admin_id',\n            width: 100,\n            render: (adminId)=>adminId || '系统'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        title: `${username} 的令牌使用记录`,\n        open: visible,\n        onCancel: onClose,\n        footer: null,\n        width: 1000,\n        destroyOnClose: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            value: filters.actionType,\n                            onChange: (value)=>{\n                                setFilters({\n                                    ...filters,\n                                    actionType: value\n                                });\n                                setTimeout(handleFilterChange, 100);\n                            },\n                            style: {\n                                width: 120\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                    value: \"all\",\n                                    children: \"全部\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\TokenUsageModal.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                    value: \"recharge\",\n                                    children: \"充值\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\TokenUsageModal.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                    value: \"consume\",\n                                    children: \"消费\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\TokenUsageModal.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\TokenUsageModal.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RangePicker, {\n                            value: filters.dateRange,\n                            onChange: (dates)=>{\n                                setFilters({\n                                    ...filters,\n                                    dateRange: dates\n                                });\n                                setTimeout(handleFilterChange, 100);\n                            },\n                            placeholder: [\n                                '开始日期',\n                                '结束日期'\n                            ]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\TokenUsageModal.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ReloadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\TokenUsageModal.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: handleFilterChange,\n                            children: \"刷新\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\TokenUsageModal.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\TokenUsageModal.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\TokenUsageModal.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_DatePicker_Modal_Select_Space_Table_Tag_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                columns: columns,\n                dataSource: records,\n                rowKey: \"id\",\n                loading: loading,\n                pagination: {\n                    ...pagination,\n                    showSizeChanger: true,\n                    showQuickJumper: true,\n                    showTotal: (total, range)=>`第 ${range[0]}-${range[1]} 条，共 ${total} 条`\n                },\n                onChange: (paginationInfo)=>{\n                    loadTokenUsage(paginationInfo.current, paginationInfo.pageSize);\n                },\n                size: \"small\"\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\TokenUsageModal.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\TokenUsageModal.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/admin/TokenUsageModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/admin/UserManagement.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/UserManagement.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/HistoryOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/StopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _TokenUsageModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenUsageModal */ \"(ssr)/./src/components/admin/TokenUsageModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst { Title, Text } = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Option } = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction UserManagement() {\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 1,\n        pageSize: 20,\n        total: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 模态框状态\n    const [createModalVisible, setCreateModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rechargeModalVisible, setRechargeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tokenUsageModalVisible, setTokenUsageModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表单实例\n    const [createForm] = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [rechargeForm] = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    // 加载用户列表\n    const loadUsers = async (page = 1, pageSize = 20)=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.getAllUsers(page, pageSize);\n            setUsers(response.users);\n            setPagination({\n                current: page,\n                pageSize,\n                total: response.pagination.total\n            });\n        } catch (error) {\n            console.error('加载用户列表失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('加载用户列表失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载用户统计\n    const loadStats = async ()=>{\n        try {\n            // 计算统计信息\n            const totalUsers = users.length;\n            const activeUsers = users.filter((u)=>u.status === 'active').length;\n            const adminUsers = users.filter((u)=>u.role === 'admin').length;\n            const totalTokens = users.reduce((sum, u)=>sum + u.tokens, 0);\n            const avgTokens = totalUsers > 0 ? totalTokens / totalUsers : 0;\n            setStats({\n                total_users: totalUsers,\n                active_users: activeUsers,\n                admin_users: adminUsers,\n                total_tokens: totalTokens,\n                avg_tokens: avgTokens\n            });\n        } catch (error) {\n            console.error('加载统计信息失败:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserManagement.useEffect\": ()=>{\n            loadUsers();\n        }\n    }[\"UserManagement.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserManagement.useEffect\": ()=>{\n            if (users.length > 0) {\n                loadStats();\n            }\n        }\n    }[\"UserManagement.useEffect\"], [\n        users\n    ]);\n    // 创建用户\n    const handleCreateUser = async (values)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.createUser(values);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('用户创建成功');\n            setCreateModalVisible(false);\n            createForm.resetFields();\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            console.error('创建用户失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(error.response?.data?.message || '创建用户失败');\n        }\n    };\n    // 充值令牌\n    const handleRecharge = async (values)=>{\n        if (!selectedUser) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.rechargeTokens(selectedUser.id, values.amount, values.description);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('充值成功');\n            setRechargeModalVisible(false);\n            rechargeForm.resetFields();\n            setSelectedUser(null);\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            console.error('充值失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(error.response?.data?.message || '充值失败');\n        }\n    };\n    // 更新用户状态\n    const handleUpdateStatus = async (userId, status)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.updateUserStatus(userId, status);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('用户状态更新成功');\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            console.error('更新用户状态失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(error.response?.data?.message || '更新用户状态失败');\n        }\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: '用户ID',\n            dataIndex: 'id',\n            key: 'id',\n            width: 100,\n            render: (id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                    code: true,\n                    children: id\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '用户名',\n            dataIndex: 'username',\n            key: 'username',\n            width: 150\n        },\n        {\n            title: '角色',\n            dataIndex: 'role',\n            key: 'role',\n            width: 100,\n            render: (role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    color: role === 'admin' ? 'red' : 'blue',\n                    children: role === 'admin' ? '管理员' : '普通用户'\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '令牌余额',\n            dataIndex: 'tokens',\n            key: 'tokens',\n            width: 120,\n            render: (tokens)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    count: tokens,\n                    showZero: true,\n                    color: \"green\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '状态',\n            dataIndex: 'status',\n            key: 'status',\n            width: 100,\n            render: (status)=>{\n                const statusConfig = {\n                    active: {\n                        color: 'success',\n                        text: '正常'\n                    },\n                    inactive: {\n                        color: 'warning',\n                        text: '停用'\n                    },\n                    banned: {\n                        color: 'error',\n                        text: '封禁'\n                    }\n                };\n                const config = statusConfig[status];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            title: '创建时间',\n            dataIndex: 'created_at',\n            key: 'created_at',\n            width: 180,\n            render: (date)=>new Date(date).toLocaleString('zh-CN')\n        },\n        {\n            title: '最后登录',\n            dataIndex: 'last_login',\n            key: 'last_login',\n            width: 180,\n            render: (date)=>date ? new Date(date).toLocaleString('zh-CN') : '从未登录'\n        },\n        {\n            title: '操作',\n            key: 'actions',\n            width: 250,\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            title: \"查看令牌记录\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>{\n                                    setSelectedUser(record);\n                                    setTokenUsageModalVisible(true);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            title: \"充值令牌\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                type: \"primary\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>{\n                                    setSelectedUser(record);\n                                    setRechargeModalVisible(true);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        record.status === 'active' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要停用此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'inactive'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"停用用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 44\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要激活此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'active'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"激活用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 44\n                                    }, void 0),\n                                    type: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要封禁此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'banned'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"封禁用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 49\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"用户管理\"\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"总用户数\",\n                                value: stats?.total_users || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 23\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"活跃用户\",\n                                value: stats?.active_users || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#3f8600'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"管理员\",\n                                value: stats?.admin_users || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#cf1322'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"总令牌数\",\n                                value: stats?.total_tokens || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#1890ff'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 flex justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            type: \"primary\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>setCreateModalVisible(true),\n                            children: \"创建用户\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>loadUsers(pagination.current, pagination.pageSize),\n                            children: \"刷新\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                columns: columns,\n                dataSource: users,\n                rowKey: \"id\",\n                loading: loading,\n                pagination: {\n                    ...pagination,\n                    showSizeChanger: true,\n                    showQuickJumper: true,\n                    showTotal: (total, range)=>`第 ${range[0]}-${range[1]} 条，共 ${total} 条`\n                },\n                onChange: (paginationInfo)=>{\n                    loadUsers(paginationInfo.current, paginationInfo.pageSize);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 368,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                title: \"创建新用户\",\n                open: createModalVisible,\n                onCancel: ()=>{\n                    setCreateModalVisible(false);\n                    createForm.resetFields();\n                },\n                footer: null,\n                width: 500,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    form: createForm,\n                    layout: \"vertical\",\n                    onFinish: handleCreateUser,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"用户名\",\n                            name: \"username\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入用户名'\n                                },\n                                {\n                                    min: 2,\n                                    message: '用户名至少2个字符'\n                                },\n                                {\n                                    max: 20,\n                                    message: '用户名最多20个字符'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                placeholder: \"请输入用户名\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"密码\",\n                            name: \"password\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入密码'\n                                },\n                                {\n                                    min: 6,\n                                    message: '密码至少6个字符'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"].Password, {\n                                placeholder: \"请输入密码\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"初始令牌数\",\n                            name: \"tokens\",\n                            initialValue: 1000,\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入初始令牌数'\n                                },\n                                {\n                                    type: 'number',\n                                    min: 0,\n                                    message: '令牌数不能为负数'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                placeholder: \"请输入初始令牌数\",\n                                style: {\n                                    width: '100%'\n                                },\n                                min: 0,\n                                max: 999999\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            className: \"mb-0 text-right\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        onClick: ()=>{\n                                            setCreateModalVisible(false);\n                                            createForm.resetFields();\n                                        },\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        type: \"primary\",\n                                        htmlType: \"submit\",\n                                        children: \"创建\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 385,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                title: `为用户 ${selectedUser?.username} 充值令牌`,\n                open: rechargeModalVisible,\n                onCancel: ()=>{\n                    setRechargeModalVisible(false);\n                    rechargeForm.resetFields();\n                    setSelectedUser(null);\n                },\n                footer: null,\n                width: 500,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            type: \"secondary\",\n                            children: [\n                                \"当前余额: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    children: selectedUser?.tokens || 0\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 19\n                                }, this),\n                                \" 令牌\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        form: rechargeForm,\n                        layout: \"vertical\",\n                        onFinish: handleRecharge,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                label: \"充值数量\",\n                                name: \"amount\",\n                                rules: [\n                                    {\n                                        required: true,\n                                        message: '请输入充值数量'\n                                    },\n                                    {\n                                        type: 'number',\n                                        min: 1,\n                                        message: '充值数量至少为1'\n                                    }\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    placeholder: \"请输入充值数量\",\n                                    style: {\n                                        width: '100%'\n                                    },\n                                    min: 1,\n                                    max: 999999\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                label: \"充值说明\",\n                                name: \"description\",\n                                initialValue: \"管理员充值\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"].TextArea, {\n                                    placeholder: \"请输入充值说明（可选）\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                className: \"mb-0 text-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            onClick: ()=>{\n                                                setRechargeModalVisible(false);\n                                                rechargeForm.resetFields();\n                                                setSelectedUser(null);\n                                            },\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            type: \"primary\",\n                                            htmlType: \"submit\",\n                                            children: \"确认充值\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 457,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenUsageModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                visible: tokenUsageModalVisible,\n                onClose: ()=>{\n                    setTokenUsageModalVisible(false);\n                    setSelectedUser(null);\n                },\n                userId: selectedUser?.id || '',\n                username: selectedUser?.username || ''\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 524,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/admin/UserManagement.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"(ssr)/../node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 检查本地存储的用户信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": async ()=>{\n                    try {\n                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get('token');\n                        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get('user');\n                        if (token && savedUser) {\n                            const userData = JSON.parse(savedUser);\n                            setUser(userData);\n                            // 验证token是否仍然有效\n                            try {\n                                await refreshUser();\n                            } catch (error) {\n                                // Token无效，清除本地存储\n                                logout();\n                            }\n                        }\n                    } catch (error) {\n                        console.error('初始化认证状态失败:', error);\n                        logout();\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (userId, password)=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.userAPI.login(userId, password);\n            if (response.token && response.user) {\n                // 保存token和用户信息\n                js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set('token', response.token, {\n                    expires: 1\n                }); // 1天过期\n                js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set('user', JSON.stringify(response.user), {\n                    expires: 1\n                });\n                setUser(response.user);\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success('登录成功');\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error('登录失败:', error);\n            const errorMessage = error.response?.data?.message || '登录失败，请检查用户ID和密码';\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(errorMessage);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove('token');\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove('user');\n        setUser(null);\n        _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"].info('已退出登录');\n    };\n    const refreshUser = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.userAPI.getProfile();\n            if (response.user) {\n                setUser(response.user);\n                js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set('user', JSON.stringify(response.user), {\n                    expires: 1\n                });\n            }\n        } catch (error) {\n            console.error('刷新用户信息失败:', error);\n            throw error;\n        }\n    };\n    const isAdmin = user?.role === 'admin';\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        refreshUser,\n        isAdmin\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvQXV0aENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFeUY7QUFDekQ7QUFDSTtBQUNMO0FBcUIvQixNQUFNUSw0QkFBY1Asb0RBQWFBLENBQThCUTtBQUV4RCxNQUFNQyxVQUFVO0lBQ3JCLE1BQU1DLFVBQVVULGlEQUFVQSxDQUFDTTtJQUMzQixJQUFJRyxZQUFZRixXQUFXO1FBQ3pCLE1BQU0sSUFBSUcsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBRTtBQU1LLE1BQU1FLGVBQTRDLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQ3BFLE1BQU0sQ0FBQ0MsTUFBTUMsUUFBUSxHQUFHYiwrQ0FBUUEsQ0FBYztJQUM5QyxNQUFNLENBQUNjLFNBQVNDLFdBQVcsR0FBR2YsK0NBQVFBLENBQUM7SUFFdkMsY0FBYztJQUNkQyxnREFBU0E7a0NBQUM7WUFDUixNQUFNZTttREFBVztvQkFDZixJQUFJO3dCQUNGLE1BQU1DLFFBQVFmLGlEQUFPQSxDQUFDZ0IsR0FBRyxDQUFDO3dCQUMxQixNQUFNQyxZQUFZakIsaURBQU9BLENBQUNnQixHQUFHLENBQUM7d0JBRTlCLElBQUlELFNBQVNFLFdBQVc7NEJBQ3RCLE1BQU1DLFdBQVdDLEtBQUtDLEtBQUssQ0FBQ0g7NEJBQzVCTixRQUFRTzs0QkFFUixnQkFBZ0I7NEJBQ2hCLElBQUk7Z0NBQ0YsTUFBTUc7NEJBQ1IsRUFBRSxPQUFPQyxPQUFPO2dDQUNkLGlCQUFpQjtnQ0FDakJDOzRCQUNGO3dCQUNGO29CQUNGLEVBQUUsT0FBT0QsT0FBTzt3QkFDZEUsUUFBUUYsS0FBSyxDQUFDLGNBQWNBO3dCQUM1QkM7b0JBQ0YsU0FBVTt3QkFDUlYsV0FBVztvQkFDYjtnQkFDRjs7WUFFQUM7UUFDRjtpQ0FBRyxFQUFFO0lBRUwsTUFBTVcsUUFBUSxPQUFPQyxRQUFnQkM7UUFDbkMsSUFBSTtZQUNGZCxXQUFXO1lBQ1gsTUFBTWUsV0FBVyxNQUFNM0IsNkNBQU9BLENBQUN3QixLQUFLLENBQUNDLFFBQVFDO1lBRTdDLElBQUlDLFNBQVNiLEtBQUssSUFBSWEsU0FBU2xCLElBQUksRUFBRTtnQkFDbkMsZUFBZTtnQkFDZlYsaURBQU9BLENBQUM2QixHQUFHLENBQUMsU0FBU0QsU0FBU2IsS0FBSyxFQUFFO29CQUFFZSxTQUFTO2dCQUFFLElBQUksT0FBTztnQkFDN0Q5QixpREFBT0EsQ0FBQzZCLEdBQUcsQ0FBQyxRQUFRVixLQUFLWSxTQUFTLENBQUNILFNBQVNsQixJQUFJLEdBQUc7b0JBQUVvQixTQUFTO2dCQUFFO2dCQUVoRW5CLFFBQVFpQixTQUFTbEIsSUFBSTtnQkFDckJSLDJFQUFPQSxDQUFDOEIsT0FBTyxDQUFDO2dCQUNoQixPQUFPO1lBQ1Q7WUFFQSxPQUFPO1FBQ1QsRUFBRSxPQUFPVixPQUFZO1lBQ25CRSxRQUFRRixLQUFLLENBQUMsU0FBU0E7WUFDdkIsTUFBTVcsZUFBZVgsTUFBTU0sUUFBUSxFQUFFTSxNQUFNaEMsV0FBVztZQUN0REEsMkVBQU9BLENBQUNvQixLQUFLLENBQUNXO1lBQ2QsT0FBTztRQUNULFNBQVU7WUFDUnBCLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTVUsU0FBUztRQUNidkIsaURBQU9BLENBQUNtQyxNQUFNLENBQUM7UUFDZm5DLGlEQUFPQSxDQUFDbUMsTUFBTSxDQUFDO1FBQ2Z4QixRQUFRO1FBQ1JULDJFQUFPQSxDQUFDa0MsSUFBSSxDQUFDO0lBQ2Y7SUFFQSxNQUFNZixjQUFjO1FBQ2xCLElBQUk7WUFDRixNQUFNTyxXQUFXLE1BQU0zQiw2Q0FBT0EsQ0FBQ29DLFVBQVU7WUFDekMsSUFBSVQsU0FBU2xCLElBQUksRUFBRTtnQkFDakJDLFFBQVFpQixTQUFTbEIsSUFBSTtnQkFDckJWLGlEQUFPQSxDQUFDNkIsR0FBRyxDQUFDLFFBQVFWLEtBQUtZLFNBQVMsQ0FBQ0gsU0FBU2xCLElBQUksR0FBRztvQkFBRW9CLFNBQVM7Z0JBQUU7WUFDbEU7UUFDRixFQUFFLE9BQU9SLE9BQU87WUFDZEUsUUFBUUYsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLE1BQU1nQixVQUFVNUIsTUFBTTZCLFNBQVM7SUFFL0IsTUFBTUMsUUFBeUI7UUFDN0I5QjtRQUNBRTtRQUNBYTtRQUNBRjtRQUNBRjtRQUNBaUI7SUFDRjtJQUVBLHFCQUNFLDhEQUFDbkMsWUFBWXNDLFFBQVE7UUFBQ0QsT0FBT0E7a0JBQzFCL0I7Ozs7OztBQUdQLEVBQUUiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm67mlofku7blpLlcXFhBUFxcZnJvbnRlbmRcXHNyY1xcY29udGV4dHNcXEF1dGhDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgQ29va2llcyBmcm9tICdqcy1jb29raWUnO1xuaW1wb3J0IHsgdXNlckFQSSB9IGZyb20gJ0AvbGliL2FwaSc7XG5pbXBvcnQgeyBtZXNzYWdlIH0gZnJvbSAnYW50ZCc7XG5cbmludGVyZmFjZSBVc2VyIHtcbiAgaWQ6IHN0cmluZztcbiAgdXNlcm5hbWU6IHN0cmluZztcbiAgcm9sZTogJ3VzZXInIHwgJ2FkbWluJztcbiAgdG9rZW5zOiBudW1iZXI7XG4gIHN0YXR1czogc3RyaW5nO1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gIGxhc3RfbG9naW4/OiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUge1xuICB1c2VyOiBVc2VyIHwgbnVsbDtcbiAgbG9hZGluZzogYm9vbGVhbjtcbiAgbG9naW46ICh1c2VySWQ6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykgPT4gUHJvbWlzZTxib29sZWFuPjtcbiAgbG9nb3V0OiAoKSA9PiB2b2lkO1xuICByZWZyZXNoVXNlcjogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgaXNBZG1pbjogYm9vbGVhbjtcbn1cblxuY29uc3QgQXV0aENvbnRleHQgPSBjcmVhdGVDb250ZXh0PEF1dGhDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTtcblxuZXhwb3J0IGNvbnN0IHVzZUF1dGggPSAoKSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufTtcblxuaW50ZXJmYWNlIEF1dGhQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGNvbnN0IEF1dGhQcm92aWRlcjogUmVhY3QuRkM8QXV0aFByb3ZpZGVyUHJvcHM+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxVc2VyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuXG4gIC8vIOajgOafpeacrOWcsOWtmOWCqOeahOeUqOaIt+S/oeaBr1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGluaXRBdXRoID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBDb29raWVzLmdldCgndG9rZW4nKTtcbiAgICAgICAgY29uc3Qgc2F2ZWRVc2VyID0gQ29va2llcy5nZXQoJ3VzZXInKTtcblxuICAgICAgICBpZiAodG9rZW4gJiYgc2F2ZWRVc2VyKSB7XG4gICAgICAgICAgY29uc3QgdXNlckRhdGEgPSBKU09OLnBhcnNlKHNhdmVkVXNlcik7XG4gICAgICAgICAgc2V0VXNlcih1c2VyRGF0YSk7XG4gICAgICAgICAgXG4gICAgICAgICAgLy8g6aqM6K+BdG9rZW7mmK/lkKbku43nhLbmnInmlYhcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgYXdhaXQgcmVmcmVzaFVzZXIoKTtcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgLy8gVG9rZW7ml6DmlYjvvIzmuIXpmaTmnKzlnLDlrZjlgqhcbiAgICAgICAgICAgIGxvZ291dCgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign5Yid5aeL5YyW6K6k6K+B54q25oCB5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgbG9nb3V0KCk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgaW5pdEF1dGgoKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGxvZ2luID0gYXN5bmMgKHVzZXJJZDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHVzZXJBUEkubG9naW4odXNlcklkLCBwYXNzd29yZCk7XG4gICAgICBcbiAgICAgIGlmIChyZXNwb25zZS50b2tlbiAmJiByZXNwb25zZS51c2VyKSB7XG4gICAgICAgIC8vIOS/neWtmHRva2Vu5ZKM55So5oi35L+h5oGvXG4gICAgICAgIENvb2tpZXMuc2V0KCd0b2tlbicsIHJlc3BvbnNlLnRva2VuLCB7IGV4cGlyZXM6IDEgfSk7IC8vIDHlpKnov4fmnJ9cbiAgICAgICAgQ29va2llcy5zZXQoJ3VzZXInLCBKU09OLnN0cmluZ2lmeShyZXNwb25zZS51c2VyKSwgeyBleHBpcmVzOiAxIH0pO1xuICAgICAgICBcbiAgICAgICAgc2V0VXNlcihyZXNwb25zZS51c2VyKTtcbiAgICAgICAgbWVzc2FnZS5zdWNjZXNzKCfnmbvlvZXmiJDlip8nKTtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICB9XG4gICAgICBcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfnmbvlvZXlpLHotKU6JywgZXJyb3IpO1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ+eZu+W9leWksei0pe+8jOivt+ajgOafpeeUqOaIt0lE5ZKM5a+G56CBJztcbiAgICAgIG1lc3NhZ2UuZXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvZ291dCA9ICgpID0+IHtcbiAgICBDb29raWVzLnJlbW92ZSgndG9rZW4nKTtcbiAgICBDb29raWVzLnJlbW92ZSgndXNlcicpO1xuICAgIHNldFVzZXIobnVsbCk7XG4gICAgbWVzc2FnZS5pbmZvKCflt7LpgIDlh7rnmbvlvZUnKTtcbiAgfTtcblxuICBjb25zdCByZWZyZXNoVXNlciA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1c2VyQVBJLmdldFByb2ZpbGUoKTtcbiAgICAgIGlmIChyZXNwb25zZS51c2VyKSB7XG4gICAgICAgIHNldFVzZXIocmVzcG9uc2UudXNlcik7XG4gICAgICAgIENvb2tpZXMuc2V0KCd1c2VyJywgSlNPTi5zdHJpbmdpZnkocmVzcG9uc2UudXNlciksIHsgZXhwaXJlczogMSB9KTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign5Yi35paw55So5oi35L+h5oGv5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBpc0FkbWluID0gdXNlcj8ucm9sZSA9PT0gJ2FkbWluJztcblxuICBjb25zdCB2YWx1ZTogQXV0aENvbnRleHRUeXBlID0ge1xuICAgIHVzZXIsXG4gICAgbG9hZGluZyxcbiAgICBsb2dpbixcbiAgICBsb2dvdXQsXG4gICAgcmVmcmVzaFVzZXIsXG4gICAgaXNBZG1pbixcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxBdXRoQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvQXV0aENvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNvb2tpZXMiLCJ1c2VyQVBJIiwibWVzc2FnZSIsIkF1dGhDb250ZXh0IiwidW5kZWZpbmVkIiwidXNlQXV0aCIsImNvbnRleHQiLCJFcnJvciIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwidXNlciIsInNldFVzZXIiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImluaXRBdXRoIiwidG9rZW4iLCJnZXQiLCJzYXZlZFVzZXIiLCJ1c2VyRGF0YSIsIkpTT04iLCJwYXJzZSIsInJlZnJlc2hVc2VyIiwiZXJyb3IiLCJsb2dvdXQiLCJjb25zb2xlIiwibG9naW4iLCJ1c2VySWQiLCJwYXNzd29yZCIsInJlc3BvbnNlIiwic2V0IiwiZXhwaXJlcyIsInN0cmluZ2lmeSIsInN1Y2Nlc3MiLCJlcnJvck1lc3NhZ2UiLCJkYXRhIiwicmVtb3ZlIiwiaW5mbyIsImdldFByb2ZpbGUiLCJpc0FkbWluIiwicm9sZSIsInZhbHVlIiwiUHJvdmlkZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conversationAPI: () => (/* binding */ conversationAPI),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   messageAPI: () => (/* binding */ messageAPI),\n/* harmony export */   systemAPI: () => (/* binding */ systemAPI),\n/* harmony export */   userAPI: () => (/* binding */ userAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n\nconst API_BASE_URL = \"http://localhost:3001/api\" || 0;\n// 创建axios实例\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 30000,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// 请求拦截器 - 添加认证token\napi.interceptors.request.use((config)=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('token');\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器 - 处理错误\napi.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response?.status === 401) {\n        // Token过期或无效，清除本地存储并跳转到登录页\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove('token');\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove('user');\n        if (false) {}\n    }\n    return Promise.reject(error);\n});\n// 用户相关API\nconst userAPI = {\n    // 登录\n    login: async (userId, password)=>{\n        const response = await api.post('/users/login', {\n            userId,\n            password\n        });\n        return response.data;\n    },\n    // 获取用户信息\n    getProfile: async ()=>{\n        const response = await api.get('/users/profile');\n        return response.data;\n    },\n    // 创建用户（管理员）\n    createUser: async (userData)=>{\n        const response = await api.post('/users', userData);\n        return response.data;\n    },\n    // 获取所有用户（管理员）\n    getAllUsers: async (page = 1, limit = 20)=>{\n        const response = await api.get(`/users?page=${page}&limit=${limit}`);\n        return response.data;\n    },\n    // 充值令牌（管理员）\n    rechargeTokens: async (userId, amount, description)=>{\n        const response = await api.post(`/users/${userId}/recharge`, {\n            amount,\n            description\n        });\n        return response.data;\n    },\n    // 更新用户状态（管理员）\n    updateUserStatus: async (userId, status)=>{\n        const response = await api.patch(`/users/${userId}/status`, {\n            status\n        });\n        return response.data;\n    },\n    // 获取用户令牌使用记录（管理员）\n    getTokenUsage: async (userId, page = 1, limit = 20, filters)=>{\n        let url = `/users/${userId}/token-usage?page=${page}&limit=${limit}`;\n        if (filters?.actionType && filters.actionType !== 'all') {\n            url += `&actionType=${filters.actionType}`;\n        }\n        if (filters?.startDate) {\n            url += `&startDate=${filters.startDate}`;\n        }\n        if (filters?.endDate) {\n            url += `&endDate=${filters.endDate}`;\n        }\n        const response = await api.get(url);\n        return response.data;\n    }\n};\n// 对话相关API\nconst conversationAPI = {\n    // 创建对话\n    create: async (title)=>{\n        const response = await api.post('/conversations', {\n            title\n        });\n        return response.data;\n    },\n    // 获取用户对话列表\n    getUserConversations: async (status = 'active')=>{\n        const response = await api.get(`/conversations/my?status=${status}`);\n        return response.data;\n    },\n    // 获取对话详情\n    getConversation: async (conversationId, page = 1, limit = 50)=>{\n        const response = await api.get(`/conversations/${conversationId}?page=${page}&limit=${limit}`);\n        return response.data;\n    },\n    // 更新对话标题\n    updateTitle: async (conversationId, title)=>{\n        const response = await api.patch(`/conversations/${conversationId}/title`, {\n            title\n        });\n        return response.data;\n    },\n    // 删除对话\n    delete: async (conversationId)=>{\n        const response = await api.delete(`/conversations/${conversationId}`);\n        return response.data;\n    },\n    // 获取所有对话（管理员）\n    getAllConversations: async (page = 1, limit = 20, filters)=>{\n        const params = new URLSearchParams({\n            page: page.toString(),\n            limit: limit.toString()\n        });\n        if (filters?.userId) params.append('userId', filters.userId);\n        if (filters?.status) params.append('status', filters.status);\n        const response = await api.get(`/conversations?${params.toString()}`);\n        return response.data;\n    }\n};\n// 消息相关API\nconst messageAPI = {\n    // 发送消息\n    sendMessage: async (conversationId, content)=>{\n        const response = await api.post('/messages', {\n            conversationId,\n            content\n        });\n        return response.data;\n    },\n    // 获取对话消息\n    getMessages: async (conversationId, page = 1, limit = 50)=>{\n        const response = await api.get(`/messages/${conversationId}?page=${page}&limit=${limit}`);\n        return response.data;\n    },\n    // 流式发送消息\n    sendMessageStream: async (conversationId, content, onChunk)=>{\n        const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('token');\n        const eventSource = new EventSource(`${API_BASE_URL}/messages/stream`, {\n            headers: {\n                'Authorization': `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            }\n        });\n        // 发送消息数据\n        await fetch(`${API_BASE_URL}/messages/stream`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                conversationId,\n                content\n            })\n        });\n        eventSource.onmessage = (event)=>{\n            try {\n                const data = JSON.parse(event.data);\n                onChunk(data);\n                if (data.type === 'completed' || data.type === 'error') {\n                    eventSource.close();\n                }\n            } catch (error) {\n                console.error('解析SSE数据失败:', error);\n            }\n        };\n        eventSource.onerror = (error)=>{\n            console.error('SSE连接错误:', error);\n            eventSource.close();\n        };\n        return eventSource;\n    }\n};\n// 系统相关API\nconst systemAPI = {\n    // 检查Ollama服务状态\n    checkOllamaHealth: async ()=>{\n        const response = await api.get('/system/ollama/health');\n        return response.data;\n    },\n    // 获取可用模型列表\n    getModels: async ()=>{\n        const response = await api.get('/system/ollama/models');\n        return response.data;\n    },\n    // 获取系统统计信息\n    getStats: async (startDate, endDate)=>{\n        const params = new URLSearchParams();\n        if (startDate) params.append('startDate', startDate);\n        if (endDate) params.append('endDate', endDate);\n        const response = await api.get(`/system/stats?${params.toString()}`);\n        return response.data;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js":
/*!**********************************************************************************!*\
  !*** __barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfigProvider: () => (/* reexport safe */ _config_provider__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config-provider */ \"(ssr)/./node_modules/antd/es/config-provider/index.js\");\n/* __next_internal_client_entry_do_not_use__ ConfigProvider auto */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Db25maWdQcm92aWRlciE9IS4vbm9kZV9tb2R1bGVzL2FudGQvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7b0VBRTZEIiwic291cmNlcyI6WyJEOlxc6aG555uu5paH5Lu25aS5XFxYQVBcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGFudGRcXGVzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb25maWdQcm92aWRlciB9IGZyb20gXCIuL2NvbmZpZy1wcm92aWRlclwiIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJDb25maWdQcm92aWRlciJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=ConfigProvider!=!./node_modules/antd/es/index.js\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/antd","vendor-chunks/@ant-design","vendor-chunks/rc-picker","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/rc-util","vendor-chunks/@babel","vendor-chunks/rc-motion","vendor-chunks/stylis","vendor-chunks/rc-notification","vendor-chunks/rc-pagination","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/js-cookie","vendor-chunks/@emotion","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/classnames","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@rc-component","vendor-chunks/rc-field-form","vendor-chunks/rc-menu","vendor-chunks/rc-tabs","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-textarea","vendor-chunks/rc-input","vendor-chunks/rc-overflow","vendor-chunks/rc-collapse","vendor-chunks/rc-resize-observer","vendor-chunks/rc-dropdown","vendor-chunks/rc-tooltip","vendor-chunks/throttle-debounce","vendor-chunks/copy-to-clipboard","vendor-chunks/compute-scroll-into-view","vendor-chunks/scroll-into-view-if-needed","vendor-chunks/toggle-selection","vendor-chunks/rc-table","vendor-chunks/rc-select","vendor-chunks/rc-virtual-list","vendor-chunks/rc-tree","vendor-chunks/rc-dialog","vendor-chunks/dayjs","vendor-chunks/rc-input-number","vendor-chunks/rc-checkbox"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%E6%96%87%E4%BB%B6%E5%A4%B9%5CXAP%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();