const mysql = require('mysql2/promise');

async function checkTable() {
  const connection = await mysql.createConnection({
    host: '************',
    user: 'sysdb',
    password: 'Roskfl2023',
    database: 'xiaoaiPlus'
  });
  
  try {
    // 检查表是否存在
    const [tables] = await connection.execute("SHOW TABLES LIKE 'token_usage'");
    console.log('表存在:', tables.length > 0);
    
    if (tables.length > 0) {
      // 检查表结构
      const [columns] = await connection.execute('DESCRIBE token_usage');
      console.log('表结构:');
      columns.forEach(col => {
        console.log(`  ${col.Field}: ${col.Type} ${col.Null} ${col.Key} ${col.Default}`);
      });
      
      // 检查数据
      const [rows] = await connection.execute('SELECT COUNT(*) as count FROM token_usage');
      console.log('数据行数:', rows[0].count);
      
      // 显示前几条记录
      const [records] = await connection.execute('SELECT * FROM token_usage LIMIT 5');
      console.log('前5条记录:');
      records.forEach(record => {
        console.log(`  ${record.id}: ${record.action_type} ${record.amount} tokens`);
      });
    } else {
      console.log('token_usage表不存在');
    }
  } catch (error) {
    console.error('检查失败:', error.message);
  } finally {
    await connection.end();
  }
}

checkTable();
