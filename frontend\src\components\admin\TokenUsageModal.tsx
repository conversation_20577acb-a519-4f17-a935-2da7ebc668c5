'use client';

import { useState, useEffect } from 'react';
import {
  Modal,
  Table,
  Tag,
  Typography,
  Space,
  Button,
  DatePicker,
  Select,
  message
} from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { userAPI } from '@/lib/api';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface TokenUsageRecord {
  id: string;
  user_id: string;
  action_type: 'consume' | 'recharge';
  amount: number;
  balance_before: number;
  balance_after: number;
  description: string;
  admin_id?: string;
  created_at: string;
}

interface TokenUsageModalProps {
  visible: boolean;
  onClose: () => void;
  userId: string;
  username: string;
}

export default function TokenUsageModal({ visible, onClose, userId, username }: TokenUsageModalProps) {
  const [records, setRecords] = useState<TokenUsageRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [filters, setFilters] = useState({
    actionType: 'all',
    dateRange: null as any,
  });

  // 加载令牌使用记录
  const loadTokenUsage = async (page = 1, pageSize = 20) => {
    try {
      setLoading(true);

      // 构建过滤器参数
      const filterParams: any = {
        actionType: filters.actionType,
      };

      if (filters.dateRange && filters.dateRange.length === 2) {
        filterParams.startDate = filters.dateRange[0].format('YYYY-MM-DD');
        filterParams.endDate = filters.dateRange[1].format('YYYY-MM-DD');
      }

      const response = await userAPI.getTokenUsage(userId, page, pageSize, filterParams);

      setRecords(response.records);
      setPagination({
        current: page,
        pageSize,
        total: response.pagination.total,
      });
    } catch (error) {
      console.error('加载令牌使用记录失败:', error);
      message.error('加载令牌使用记录失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && userId) {
      loadTokenUsage();
    }
  }, [visible, userId]);

  // 当过滤器改变时重新加载数据
  const handleFilterChange = () => {
    loadTokenUsage(1, pagination.pageSize);
  };

  // 表格列定义
  const columns: ColumnsType<TokenUsageRecord> = [
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date: string) => new Date(date).toLocaleString('zh-CN'),
    },
    {
      title: '操作类型',
      dataIndex: 'action_type',
      key: 'action_type',
      width: 100,
      render: (type: string) => (
        <Tag color={type === 'recharge' ? 'green' : 'red'}>
          {type === 'recharge' ? '充值' : '消费'}
        </Tag>
      ),
    },
    {
      title: '数量',
      dataIndex: 'amount',
      key: 'amount',
      width: 100,
      render: (amount: number, record: TokenUsageRecord) => (
        <Text style={{ color: record.action_type === 'recharge' ? '#52c41a' : '#ff4d4f' }}>
          {record.action_type === 'recharge' ? '+' : '-'}{amount}
        </Text>
      ),
    },
    {
      title: '变更前余额',
      dataIndex: 'balance_before',
      key: 'balance_before',
      width: 120,
    },
    {
      title: '变更后余额',
      dataIndex: 'balance_after',
      key: 'balance_after',
      width: 120,
    },
    {
      title: '说明',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '操作员',
      dataIndex: 'admin_id',
      key: 'admin_id',
      width: 100,
      render: (adminId: string) => adminId || '系统',
    },
  ];

  return (
    <Modal
      title={`${username} 的令牌使用记录`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      destroyOnClose
    >
      {/* 筛选器 */}
      <div className="mb-4">
        <Space>
          <Select
            value={filters.actionType}
            onChange={(value) => {
              setFilters({ ...filters, actionType: value });
              setTimeout(handleFilterChange, 100);
            }}
            style={{ width: 120 }}
          >
            <Option value="all">全部</Option>
            <Option value="recharge">充值</Option>
            <Option value="consume">消费</Option>
          </Select>

          <RangePicker
            value={filters.dateRange}
            onChange={(dates) => {
              setFilters({ ...filters, dateRange: dates });
              setTimeout(handleFilterChange, 100);
            }}
            placeholder={['开始日期', '结束日期']}
          />

          <Button
            icon={<ReloadOutlined />}
            onClick={handleFilterChange}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 记录表格 */}
      <Table
        columns={columns}
        dataSource={records}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }}
        onChange={(paginationInfo) => {
          loadTokenUsage(paginationInfo.current!, paginationInfo.pageSize!);
        }}
        size="small"
      />
    </Modal>
  );
}
