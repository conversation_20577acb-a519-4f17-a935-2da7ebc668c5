{"name": "xap-backend", "version": "1.0.0", "description": "AI学习和咨询系统后端API服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/initDatabase.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ai", "chat", "prison", "education"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "ollama": "^0.5.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}}