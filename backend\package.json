{"name": "xap-backend", "version": "1.0.0", "description": "AI学习和咨询系统后端API服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/initDatabase.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ai", "chat", "prison", "education"], "author": "", "license": "ISC", "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@types/js-cookie": "^3.0.6", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.5", "ollama": "^0.5.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}}