const { query } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

class Message {
  // 根据ID查找消息
  static async findById(id) {
    const sql = 'SELECT * FROM messages WHERE id = ?';
    const messages = await query(sql, [id]);
    return messages[0] || null;
  }

  // 创建新消息
  static async create(messageData) {
    const { conversationId, userId, role, content, tokensUsed = 0 } = messageData;
    
    const id = uuidv4();
    const sql = `
      INSERT INTO messages (id, conversation_id, user_id, role, content, tokens_used)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    await query(sql, [id, conversationId, userId, role, content, tokensUsed]);
    return await this.findById(id);
  }

  // 获取对话的所有消息
  static async getByConversationId(conversationId, page = 1, limit = 50) {
    const offset = (page - 1) * limit;
    
    const countSql = 'SELECT COUNT(*) as total FROM messages WHERE conversation_id = ?';
    const countResult = await query(countSql, [conversationId]);
    const total = countResult[0].total;

    const sql = `
      SELECT * FROM messages
      WHERE conversation_id = ?
      ORDER BY created_at ASC
      LIMIT ? OFFSET ?
    `;
    
    const messages = await query(sql, [conversationId, limit, offset]);

    return {
      messages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  // 获取用户的所有消息
  static async getByUserId(userId, page = 1, limit = 100) {
    const offset = (page - 1) * limit;
    
    const countSql = 'SELECT COUNT(*) as total FROM messages WHERE user_id = ?';
    const countResult = await query(countSql, [userId]);
    const total = countResult[0].total;

    const sql = `
      SELECT m.*, c.title as conversation_title
      FROM messages m
      LEFT JOIN conversations c ON m.conversation_id = c.id
      WHERE m.user_id = ?
      ORDER BY m.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const messages = await query(sql, [userId, limit, offset]);

    return {
      messages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  // 获取对话的最后一条消息
  static async getLastMessage(conversationId) {
    const sql = `
      SELECT * FROM messages
      WHERE conversation_id = ?
      ORDER BY created_at DESC
      LIMIT 1
    `;
    
    const messages = await query(sql, [conversationId]);
    return messages[0] || null;
  }

  // 删除消息
  static async delete(id) {
    const sql = 'DELETE FROM messages WHERE id = ?';
    await query(sql, [id]);
  }

  // 删除对话的所有消息
  static async deleteByConversationId(conversationId) {
    const sql = 'DELETE FROM messages WHERE conversation_id = ?';
    await query(sql, [conversationId]);
  }

  // 获取用户的令牌使用统计
  static async getTokenUsageStats(userId, startDate = null, endDate = null) {
    let whereClause = 'WHERE user_id = ? AND role = "user"';
    const params = [userId];

    if (startDate) {
      whereClause += ' AND created_at >= ?';
      params.push(startDate);
    }

    if (endDate) {
      whereClause += ' AND created_at <= ?';
      params.push(endDate);
    }

    const sql = `
      SELECT 
        COUNT(*) as message_count,
        SUM(tokens_used) as total_tokens_used,
        AVG(tokens_used) as avg_tokens_per_message,
        DATE(created_at) as date
      FROM messages
      ${whereClause}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
    
    return await query(sql, params);
  }

  // 获取系统的令牌使用统计（管理员功能）
  static async getSystemTokenUsageStats(startDate = null, endDate = null) {
    let whereClause = 'WHERE role = "user"';
    const params = [];

    if (startDate) {
      whereClause += ' AND created_at >= ?';
      params.push(startDate);
    }

    if (endDate) {
      whereClause += ' AND created_at <= ?';
      params.push(endDate);
    }

    const sql = `
      SELECT 
        COUNT(*) as total_messages,
        SUM(tokens_used) as total_tokens_used,
        AVG(tokens_used) as avg_tokens_per_message,
        COUNT(DISTINCT user_id) as active_users,
        DATE(created_at) as date
      FROM messages
      ${whereClause}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
    
    return await query(sql, params);
  }

  // 搜索消息内容
  static async search(searchTerm, userId = null, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE content LIKE ?';
    const params = [`%${searchTerm}%`];

    if (userId) {
      whereClause += ' AND user_id = ?';
      params.push(userId);
    }

    const countSql = `SELECT COUNT(*) as total FROM messages ${whereClause}`;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;

    const sql = `
      SELECT m.*, c.title as conversation_title, u.username
      FROM messages m
      LEFT JOIN conversations c ON m.conversation_id = c.id
      LEFT JOIN users u ON m.user_id = u.id
      ${whereClause}
      ORDER BY m.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(limit, offset);
    const messages = await query(sql, params);

    return {
      messages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }
}

module.exports = Message;
