const Conversation = require('../models/Conversation');
const Message = require('../models/Message');

// 创建新对话
const createConversation = async (req, res) => {
  try {
    const { title } = req.body;
    const userId = req.user.id;

    if (!title || title.trim().length === 0) {
      return res.status(400).json({
        error: '参数错误',
        message: '对话标题不能为空'
      });
    }

    const conversation = await Conversation.create(userId, title.trim());

    res.status(201).json({
      message: '对话创建成功',
      conversation
    });
  } catch (error) {
    console.error('创建对话错误:', error);
    
    if (error.message.includes('最多只能有10个对话')) {
      return res.status(409).json({
        error: '对话数量限制',
        message: error.message
      });
    }

    res.status(500).json({
      error: '服务器错误',
      message: '创建对话时发生错误'
    });
  }
};

// 获取用户的对话列表
const getUserConversations = async (req, res) => {
  try {
    const userId = req.user.id;
    const status = req.query.status || 'active';

    const conversations = await Conversation.getByUserId(userId, status);

    res.json({
      message: '获取对话列表成功',
      conversations
    });
  } catch (error) {
    console.error('获取对话列表错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '获取对话列表时发生错误'
    });
  }
};

// 获取对话详情
const getConversation = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;

    // 检查对话是否存在
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return res.status(404).json({
        error: '对话不存在',
        message: '找不到指定的对话'
      });
    }

    // 检查权限（普通用户只能查看自己的对话）
    if (req.user.role !== 'admin' && conversation.user_id !== userId) {
      return res.status(403).json({
        error: '权限不足',
        message: '您只能查看自己的对话'
      });
    }

    // 获取对话消息
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const messagesResult = await Message.getByConversationId(conversationId, page, limit);

    res.json({
      message: '获取对话详情成功',
      conversation,
      ...messagesResult
    });
  } catch (error) {
    console.error('获取对话详情错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '获取对话详情时发生错误'
    });
  }
};

// 更新对话标题
const updateConversationTitle = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { title } = req.body;
    const userId = req.user.id;

    if (!title || title.trim().length === 0) {
      return res.status(400).json({
        error: '参数错误',
        message: '对话标题不能为空'
      });
    }

    // 检查对话是否存在且属于当前用户
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return res.status(404).json({
        error: '对话不存在',
        message: '找不到指定的对话'
      });
    }

    if (req.user.role !== 'admin' && conversation.user_id !== userId) {
      return res.status(403).json({
        error: '权限不足',
        message: '您只能修改自己的对话'
      });
    }

    const updatedConversation = await Conversation.updateTitle(conversationId, title.trim());

    res.json({
      message: '对话标题更新成功',
      conversation: updatedConversation
    });
  } catch (error) {
    console.error('更新对话标题错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '更新对话标题时发生错误'
    });
  }
};

// 删除对话
const deleteConversation = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;

    // 检查对话是否存在且属于当前用户
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return res.status(404).json({
        error: '对话不存在',
        message: '找不到指定的对话'
      });
    }

    if (req.user.role !== 'admin' && conversation.user_id !== userId) {
      return res.status(403).json({
        error: '权限不足',
        message: '您只能删除自己的对话'
      });
    }

    await Conversation.delete(conversationId);

    res.json({
      message: '对话删除成功',
      conversationId
    });
  } catch (error) {
    console.error('删除对话错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '删除对话时发生错误'
    });
  }
};

// 获取所有对话（管理员功能）
const getAllConversations = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const filters = {};

    if (req.query.userId) {
      filters.userId = req.query.userId;
    }

    if (req.query.status) {
      filters.status = req.query.status;
    }

    const result = await Conversation.getAll(page, limit, filters);

    res.json({
      message: '获取对话列表成功',
      ...result
    });
  } catch (error) {
    console.error('获取所有对话错误:', error);
    res.status(500).json({
      error: '服务器错误',
      message: '获取对话列表时发生错误'
    });
  }
};

module.exports = {
  createConversation,
  getUserConversations,
  getConversation,
  updateConversationTitle,
  deleteConversation,
  getAllConversations
};
