"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/UserManagement.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/UserManagement.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/HistoryOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/StopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _TokenUsageModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenUsageModal */ \"(app-pages-browser)/./src/components/admin/TokenUsageModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Title, Text } = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Option } = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction UserManagement() {\n    _s();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 1,\n        pageSize: 20,\n        total: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 模态框状态\n    const [createModalVisible, setCreateModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rechargeModalVisible, setRechargeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tokenUsageModalVisible, setTokenUsageModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表单实例\n    const [createForm] = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [rechargeForm] = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    // 用户名检查状态\n    const [usernameCheckStatus, setUsernameCheckStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [usernameCheckMessage, setUsernameCheckMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 加载用户列表\n    const loadUsers = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, pageSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.getAllUsers(page, pageSize);\n            setUsers(response.users);\n            setPagination({\n                current: page,\n                pageSize,\n                total: response.pagination.total\n            });\n        } catch (error) {\n            console.error('加载用户列表失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('加载用户列表失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载用户统计\n    const loadStats = async ()=>{\n        try {\n            // 计算统计信息\n            const totalUsers = users.length;\n            const activeUsers = users.filter((u)=>u.status === 'active').length;\n            const adminUsers = users.filter((u)=>u.role === 'admin').length;\n            const totalTokens = users.reduce((sum, u)=>sum + u.tokens, 0);\n            const avgTokens = totalUsers > 0 ? totalTokens / totalUsers : 0;\n            setStats({\n                total_users: totalUsers,\n                active_users: activeUsers,\n                admin_users: adminUsers,\n                total_tokens: totalTokens,\n                avg_tokens: avgTokens\n            });\n        } catch (error) {\n            console.error('加载统计信息失败:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserManagement.useEffect\": ()=>{\n            loadUsers();\n        }\n    }[\"UserManagement.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserManagement.useEffect\": ()=>{\n            if (users.length > 0) {\n                loadStats();\n            }\n        }\n    }[\"UserManagement.useEffect\"], [\n        users\n    ]);\n    // 检查用户名可用性\n    const checkUsernameAvailability = async (username)=>{\n        if (!username || username.length < 3) {\n            setUsernameCheckStatus('');\n            setUsernameCheckMessage('');\n            return;\n        }\n        setUsernameCheckStatus('validating');\n        setUsernameCheckMessage('检查中...');\n        try {\n            const result = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.checkUsernameAvailability(username);\n            if (result.available) {\n                setUsernameCheckStatus('success');\n                setUsernameCheckMessage('用户名可用');\n            } else {\n                setUsernameCheckStatus('error');\n                setUsernameCheckMessage(result.message || '用户名不可用');\n            }\n        } catch (error) {\n            setUsernameCheckStatus('error');\n            setUsernameCheckMessage('检查失败，请重试');\n        }\n    };\n    // 创建用户\n    const handleCreateUser = async (values)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.createUser(values);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('用户创建成功');\n            setCreateModalVisible(false);\n            createForm.resetFields();\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            console.error('创建用户失败:', error);\n            if (error.response && error.response.status === 409) {\n                var _error_response_data;\n                _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '用户名已存在，请更换用户名');\n            } else {\n                var _error_response_data1, _error_response;\n                _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data1 = _error_response.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || '创建用户失败');\n            }\n        }\n    };\n    // 充值令牌\n    const handleRecharge = async (values)=>{\n        if (!selectedUser) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.rechargeTokens(selectedUser.id, values.amount, values.description);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('充值成功');\n            setRechargeModalVisible(false);\n            rechargeForm.resetFields();\n            setSelectedUser(null);\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('充值失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '充值失败');\n        }\n    };\n    // 更新用户状态\n    const handleUpdateStatus = async (userId, status)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.updateUserStatus(userId, status);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('用户状态更新成功');\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('更新用户状态失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '更新用户状态失败');\n        }\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: '用户ID',\n            dataIndex: 'id',\n            key: 'id',\n            width: 100,\n            render: (id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                    code: true,\n                    children: id\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '用户名',\n            dataIndex: 'username',\n            key: 'username',\n            width: 150\n        },\n        {\n            title: '角色',\n            dataIndex: 'role',\n            key: 'role',\n            width: 100,\n            render: (role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    color: role === 'admin' ? 'red' : 'blue',\n                    children: role === 'admin' ? '管理员' : '普通用户'\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '令牌余额',\n            dataIndex: 'tokens',\n            key: 'tokens',\n            width: 120,\n            render: (tokens)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    count: tokens,\n                    showZero: true,\n                    color: \"green\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '状态',\n            dataIndex: 'status',\n            key: 'status',\n            width: 100,\n            render: (status)=>{\n                const statusConfig = {\n                    active: {\n                        color: 'success',\n                        text: '正常'\n                    },\n                    inactive: {\n                        color: 'warning',\n                        text: '停用'\n                    },\n                    banned: {\n                        color: 'error',\n                        text: '封禁'\n                    }\n                };\n                const config = statusConfig[status];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            title: '创建时间',\n            dataIndex: 'created_at',\n            key: 'created_at',\n            width: 180,\n            render: (date)=>new Date(date).toLocaleString('zh-CN')\n        },\n        {\n            title: '最后登录',\n            dataIndex: 'last_login',\n            key: 'last_login',\n            width: 180,\n            render: (date)=>date ? new Date(date).toLocaleString('zh-CN') : '从未登录'\n        },\n        {\n            title: '操作',\n            key: 'actions',\n            width: 250,\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            title: \"查看令牌记录\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>{\n                                    setSelectedUser(record);\n                                    setTokenUsageModalVisible(true);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            title: \"充值令牌\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                type: \"primary\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>{\n                                    setSelectedUser(record);\n                                    setRechargeModalVisible(true);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        record.status === 'active' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要停用此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'inactive'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"停用用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 44\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要激活此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'active'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"激活用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 44\n                                    }, void 0),\n                                    type: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要封禁此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'banned'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"封禁用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 49\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"用户管理\"\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"总用户数\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.total_users) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 23\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"活跃用户\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.active_users) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#3f8600'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"管理员\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.admin_users) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#cf1322'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"总令牌数\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.total_tokens) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#1890ff'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 flex justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            type: \"primary\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>setCreateModalVisible(true),\n                            children: \"创建用户\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>loadUsers(pagination.current, pagination.pageSize),\n                            children: \"刷新\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 383,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                columns: columns,\n                dataSource: users,\n                rowKey: \"id\",\n                loading: loading,\n                pagination: {\n                    ...pagination,\n                    showSizeChanger: true,\n                    showQuickJumper: true,\n                    showTotal: (total, range)=>\"第 \".concat(range[0], \"-\").concat(range[1], \" 条，共 \").concat(total, \" 条\")\n                },\n                onChange: (paginationInfo)=>{\n                    loadUsers(paginationInfo.current, paginationInfo.pageSize);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 402,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                title: \"创建新用户\",\n                open: createModalVisible,\n                onCancel: ()=>{\n                    setCreateModalVisible(false);\n                    createForm.resetFields();\n                },\n                footer: null,\n                width: 500,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    form: createForm,\n                    layout: \"vertical\",\n                    onFinish: handleCreateUser,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"用户名\",\n                            name: \"username\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入用户名'\n                                },\n                                {\n                                    min: 2,\n                                    message: '用户名至少2个字符'\n                                },\n                                {\n                                    max: 20,\n                                    message: '用户名最多20个字符'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                placeholder: \"请输入用户名\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"密码\",\n                            name: \"password\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入密码'\n                                },\n                                {\n                                    min: 6,\n                                    message: '密码至少6个字符'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"].Password, {\n                                placeholder: \"请输入密码\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"初始令牌数\",\n                            name: \"tokens\",\n                            initialValue: 1000,\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入初始令牌数'\n                                },\n                                {\n                                    type: 'number',\n                                    min: 0,\n                                    message: '令牌数不能为负数'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                placeholder: \"请输入初始令牌数\",\n                                style: {\n                                    width: '100%'\n                                },\n                                min: 0,\n                                max: 999999\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            className: \"mb-0 text-right\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        onClick: ()=>{\n                                            setCreateModalVisible(false);\n                                            createForm.resetFields();\n                                        },\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        type: \"primary\",\n                                        htmlType: \"submit\",\n                                        children: \"创建\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                title: \"为用户 \".concat(selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.username, \" 充值令牌\"),\n                open: rechargeModalVisible,\n                onCancel: ()=>{\n                    setRechargeModalVisible(false);\n                    rechargeForm.resetFields();\n                    setSelectedUser(null);\n                },\n                footer: null,\n                width: 500,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            type: \"secondary\",\n                            children: [\n                                \"当前余额: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    children: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.tokens) || 0\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 19\n                                }, this),\n                                \" 令牌\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        form: rechargeForm,\n                        layout: \"vertical\",\n                        onFinish: handleRecharge,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                label: \"充值数量\",\n                                name: \"amount\",\n                                rules: [\n                                    {\n                                        required: true,\n                                        message: '请输入充值数量'\n                                    },\n                                    {\n                                        type: 'number',\n                                        min: 1,\n                                        message: '充值数量至少为1'\n                                    }\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    placeholder: \"请输入充值数量\",\n                                    style: {\n                                        width: '100%'\n                                    },\n                                    min: 1,\n                                    max: 999999\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                label: \"充值说明\",\n                                name: \"description\",\n                                initialValue: \"管理员充值\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"].TextArea, {\n                                    placeholder: \"请输入充值说明（可选）\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                className: \"mb-0 text-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            onClick: ()=>{\n                                                setRechargeModalVisible(false);\n                                                rechargeForm.resetFields();\n                                                setSelectedUser(null);\n                                            },\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            type: \"primary\",\n                                            htmlType: \"submit\",\n                                            children: \"确认充值\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 491,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenUsageModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                visible: tokenUsageModalVisible,\n                onClose: ()=>{\n                    setTokenUsageModalVisible(false);\n                    setSelectedUser(null);\n                },\n                userId: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.id) || '',\n                username: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.username) || ''\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 558,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, this);\n}\n_s(UserManagement, \"I/sukycfvf8mdqQ6L2WswooZtBI=\", false, function() {\n    return [\n        _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm,\n        _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm\n    ];\n});\n_c = UserManagement;\nvar _c;\n$RefreshReg$(_c, \"UserManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/UserManagement.tsx\n"));

/***/ })

});