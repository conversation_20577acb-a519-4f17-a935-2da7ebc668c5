(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "conversationAPI": (()=>conversationAPI),
    "default": (()=>__TURBOPACK__default__export__),
    "messageAPI": (()=>messageAPI),
    "systemAPI": (()=>systemAPI),
    "userAPI": (()=>userAPI)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
(()=>{
    const e = new Error("Cannot find module 'axios'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
(()=>{
    const e = new Error("Cannot find module 'js-cookie'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
;
;
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:3001/api") || 'http://localhost:3001/api';
// 创建axios实例
const api = axios.create({
    baseURL: API_BASE_URL,
    timeout: 30000,
    headers: {
        'Content-Type': 'application/json'
    }
});
// 请求拦截器 - 添加认证token
api.interceptors.request.use((config)=>{
    const token = Cookies.get('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// 响应拦截器 - 处理错误
api.interceptors.response.use((response)=>{
    return response;
}, (error)=>{
    if (error.response?.status === 401) {
        // Token过期或无效，清除本地存储并跳转到登录页
        Cookies.remove('token');
        Cookies.remove('user');
        if ("TURBOPACK compile-time truthy", 1) {
            window.location.href = '/login';
        }
    }
    return Promise.reject(error);
});
const userAPI = {
    // 登录
    login: async (userId, password)=>{
        const response = await api.post('/users/login', {
            userId,
            password
        });
        return response.data;
    },
    // 获取用户信息
    getProfile: async ()=>{
        const response = await api.get('/users/profile');
        return response.data;
    },
    // 创建用户（管理员）
    createUser: async (userData)=>{
        const response = await api.post('/users', userData);
        return response.data;
    },
    // 获取所有用户（管理员）
    getAllUsers: async (page = 1, limit = 20)=>{
        const response = await api.get(`/users?page=${page}&limit=${limit}`);
        return response.data;
    },
    // 充值令牌（管理员）
    rechargeTokens: async (userId, amount, description)=>{
        const response = await api.post(`/users/${userId}/recharge`, {
            amount,
            description
        });
        return response.data;
    },
    // 更新用户状态（管理员）
    updateUserStatus: async (userId, status)=>{
        const response = await api.patch(`/users/${userId}/status`, {
            status
        });
        return response.data;
    }
};
const conversationAPI = {
    // 创建对话
    create: async (title)=>{
        const response = await api.post('/conversations', {
            title
        });
        return response.data;
    },
    // 获取用户对话列表
    getUserConversations: async (status = 'active')=>{
        const response = await api.get(`/conversations/my?status=${status}`);
        return response.data;
    },
    // 获取对话详情
    getConversation: async (conversationId, page = 1, limit = 50)=>{
        const response = await api.get(`/conversations/${conversationId}?page=${page}&limit=${limit}`);
        return response.data;
    },
    // 更新对话标题
    updateTitle: async (conversationId, title)=>{
        const response = await api.patch(`/conversations/${conversationId}/title`, {
            title
        });
        return response.data;
    },
    // 删除对话
    delete: async (conversationId)=>{
        const response = await api.delete(`/conversations/${conversationId}`);
        return response.data;
    },
    // 获取所有对话（管理员）
    getAllConversations: async (page = 1, limit = 20, filters)=>{
        const params = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString()
        });
        if (filters?.userId) params.append('userId', filters.userId);
        if (filters?.status) params.append('status', filters.status);
        const response = await api.get(`/conversations?${params.toString()}`);
        return response.data;
    }
};
const messageAPI = {
    // 发送消息
    sendMessage: async (conversationId, content)=>{
        const response = await api.post('/messages', {
            conversationId,
            content
        });
        return response.data;
    },
    // 获取对话消息
    getMessages: async (conversationId, page = 1, limit = 50)=>{
        const response = await api.get(`/messages/${conversationId}?page=${page}&limit=${limit}`);
        return response.data;
    },
    // 流式发送消息
    sendMessageStream: async (conversationId, content, onChunk)=>{
        const token = Cookies.get('token');
        const eventSource = new EventSource(`${API_BASE_URL}/messages/stream`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        // 发送消息数据
        await fetch(`${API_BASE_URL}/messages/stream`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                conversationId,
                content
            })
        });
        eventSource.onmessage = (event)=>{
            try {
                const data = JSON.parse(event.data);
                onChunk(data);
                if (data.type === 'completed' || data.type === 'error') {
                    eventSource.close();
                }
            } catch (error) {
                console.error('解析SSE数据失败:', error);
            }
        };
        eventSource.onerror = (error)=>{
            console.error('SSE连接错误:', error);
            eventSource.close();
        };
        return eventSource;
    }
};
const systemAPI = {
    // 检查Ollama服务状态
    checkOllamaHealth: async ()=>{
        const response = await api.get('/system/ollama/health');
        return response.data;
    },
    // 获取可用模型列表
    getModels: async ()=>{
        const response = await api.get('/system/ollama/models');
        return response.data;
    },
    // 获取系统统计信息
    getStats: async (startDate, endDate)=>{
        const params = new URLSearchParams();
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);
        const response = await api.get(`/system/stats?${params.toString()}`);
        return response.data;
    }
};
const __TURBOPACK__default__export__ = api;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
(()=>{
    const e = new Error("Cannot find module 'js-cookie'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/message/index.js [app-client] (ecmascript) <export default as message>");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useAuth = ()=>{
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
_s(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const AuthProvider = ({ children })=>{
    _s1();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    // 检查本地存储的用户信息
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            const initAuth = {
                "AuthProvider.useEffect.initAuth": async ()=>{
                    try {
                        const token = Cookies.get('token');
                        const savedUser = Cookies.get('user');
                        if (token && savedUser) {
                            const userData = JSON.parse(savedUser);
                            setUser(userData);
                            // 验证token是否仍然有效
                            try {
                                await refreshUser();
                            } catch (error) {
                                // Token无效，清除本地存储
                                logout();
                            }
                        }
                    } catch (error) {
                        console.error('初始化认证状态失败:', error);
                        logout();
                    } finally{
                        setLoading(false);
                    }
                }
            }["AuthProvider.useEffect.initAuth"];
            initAuth();
        }
    }["AuthProvider.useEffect"], []);
    const login = async (userId, password)=>{
        try {
            setLoading(true);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userAPI"].login(userId, password);
            if (response.token && response.user) {
                // 保存token和用户信息
                Cookies.set('token', response.token, {
                    expires: 1
                }); // 1天过期
                Cookies.set('user', JSON.stringify(response.user), {
                    expires: 1
                });
                setUser(response.user);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success('登录成功');
                return true;
            }
            return false;
        } catch (error) {
            console.error('登录失败:', error);
            const errorMessage = error.response?.data?.message || '登录失败，请检查用户ID和密码';
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].error(errorMessage);
            return false;
        } finally{
            setLoading(false);
        }
    };
    const logout = ()=>{
        Cookies.remove('token');
        Cookies.remove('user');
        setUser(null);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].info('已退出登录');
    };
    const refreshUser = async ()=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userAPI"].getProfile();
            if (response.user) {
                setUser(response.user);
                Cookies.set('user', JSON.stringify(response.user), {
                    expires: 1
                });
            }
        } catch (error) {
            console.error('刷新用户信息失败:', error);
            throw error;
        }
    };
    const isAdmin = user?.role === 'admin';
    const value = {
        user,
        loading,
        login,
        logout,
        refreshUser,
        isAdmin
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 133,
        columnNumber: 5
    }, this);
};
_s1(AuthProvider, "NiO5z6JIqzX62LS5UWDgIqbZYyY=");
_c = AuthProvider;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_444e773f._.js.map