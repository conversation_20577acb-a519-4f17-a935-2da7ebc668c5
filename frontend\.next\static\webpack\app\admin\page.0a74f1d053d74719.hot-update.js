"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/UserManagement.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/UserManagement.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Form,Input,InputNumber,Modal,Popconfirm,Row,Select,Space,Statistic,Table,Tag,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input-number/index.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/HistoryOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/StopOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,DollarOutlined,EditOutlined,HistoryOutlined,PlusOutlined,ReloadOutlined,StopOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _TokenUsageModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenUsageModal */ \"(app-pages-browser)/./src/components/admin/TokenUsageModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Title, Text } = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Option } = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction UserManagement() {\n    _s();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 1,\n        pageSize: 20,\n        total: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 模态框状态\n    const [createModalVisible, setCreateModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rechargeModalVisible, setRechargeModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tokenUsageModalVisible, setTokenUsageModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表单实例\n    const [createForm] = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [rechargeForm] = _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    // 加载用户列表\n    const loadUsers = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, pageSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.getAllUsers(page, pageSize);\n            setUsers(response.users);\n            setPagination({\n                current: page,\n                pageSize,\n                total: response.pagination.total\n            });\n        } catch (error) {\n            console.error('加载用户列表失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('加载用户列表失败');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载用户统计\n    const loadStats = async ()=>{\n        try {\n            // 计算统计信息\n            const totalUsers = users.length;\n            const activeUsers = users.filter((u)=>u.status === 'active').length;\n            const adminUsers = users.filter((u)=>u.role === 'admin').length;\n            const totalTokens = users.reduce((sum, u)=>sum + u.tokens, 0);\n            const avgTokens = totalUsers > 0 ? totalTokens / totalUsers : 0;\n            setStats({\n                total_users: totalUsers,\n                active_users: activeUsers,\n                admin_users: adminUsers,\n                total_tokens: totalTokens,\n                avg_tokens: avgTokens\n            });\n        } catch (error) {\n            console.error('加载统计信息失败:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserManagement.useEffect\": ()=>{\n            loadUsers();\n        }\n    }[\"UserManagement.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserManagement.useEffect\": ()=>{\n            if (users.length > 0) {\n                loadStats();\n            }\n        }\n    }[\"UserManagement.useEffect\"], [\n        users\n    ]);\n    // 创建用户\n    const handleCreateUser = async (values)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.createUser(values);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('用户创建成功');\n            setCreateModalVisible(false);\n            createForm.resetFields();\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            console.error('创建用户失败:', error);\n            if (error.response && error.response.status === 409) {\n                var _error_response_data;\n                _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '用户名已存在，请更换用户名');\n            } else {\n                var _error_response_data1, _error_response;\n                _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data1 = _error_response.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || '创建用户失败');\n            }\n        }\n    };\n    // 充值令牌\n    const handleRecharge = async (values)=>{\n        if (!selectedUser) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.rechargeTokens(selectedUser.id, values.amount, values.description);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('充值成功');\n            setRechargeModalVisible(false);\n            rechargeForm.resetFields();\n            setSelectedUser(null);\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('充值失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '充值失败');\n        }\n    };\n    // 更新用户状态\n    const handleUpdateStatus = async (userId, status)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.userAPI.updateUserStatus(userId, status);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('用户状态更新成功');\n            loadUsers(pagination.current, pagination.pageSize);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('更新用户状态失败:', error);\n            _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || '更新用户状态失败');\n        }\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: '用户ID',\n            dataIndex: 'id',\n            key: 'id',\n            width: 100,\n            render: (id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                    code: true,\n                    children: id\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '用户名',\n            dataIndex: 'username',\n            key: 'username',\n            width: 150\n        },\n        {\n            title: '角色',\n            dataIndex: 'role',\n            key: 'role',\n            width: 100,\n            render: (role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    color: role === 'admin' ? 'red' : 'blue',\n                    children: role === 'admin' ? '管理员' : '普通用户'\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '令牌余额',\n            dataIndex: 'tokens',\n            key: 'tokens',\n            width: 120,\n            render: (tokens)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    count: tokens,\n                    showZero: true,\n                    color: \"green\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            title: '状态',\n            dataIndex: 'status',\n            key: 'status',\n            width: 100,\n            render: (status)=>{\n                const statusConfig = {\n                    active: {\n                        color: 'success',\n                        text: '正常'\n                    },\n                    inactive: {\n                        color: 'warning',\n                        text: '停用'\n                    },\n                    banned: {\n                        color: 'error',\n                        text: '封禁'\n                    }\n                };\n                const config = statusConfig[status];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            title: '创建时间',\n            dataIndex: 'created_at',\n            key: 'created_at',\n            width: 180,\n            render: (date)=>new Date(date).toLocaleString('zh-CN')\n        },\n        {\n            title: '最后登录',\n            dataIndex: 'last_login',\n            key: 'last_login',\n            width: 180,\n            render: (date)=>date ? new Date(date).toLocaleString('zh-CN') : '从未登录'\n        },\n        {\n            title: '操作',\n            key: 'actions',\n            width: 250,\n            render: (_, record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: \"small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            title: \"查看令牌记录\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>{\n                                    setSelectedUser(record);\n                                    setTokenUsageModalVisible(true);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            title: \"充值令牌\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                type: \"primary\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: ()=>{\n                                    setSelectedUser(record);\n                                    setRechargeModalVisible(true);\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        record.status === 'active' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要停用此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'inactive'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"停用用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 44\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要激活此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'active'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"激活用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 44\n                                    }, void 0),\n                                    type: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            title: \"确定要封禁此用户吗？\",\n                            onConfirm: ()=>handleUpdateStatus(record.id, 'banned'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                title: \"封禁用户\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 49\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"用户管理\"\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"总用户数\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.total_users) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 23\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"活跃用户\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.active_users) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#3f8600'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"管理员\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.admin_users) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#cf1322'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                title: \"总令牌数\",\n                                value: (stats === null || stats === void 0 ? void 0 : stats.total_tokens) || 0,\n                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 23\n                                }, void 0),\n                                valueStyle: {\n                                    color: '#1890ff'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 flex justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            type: \"primary\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>setCreateModalVisible(true),\n                            children: \"创建用户\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_DollarOutlined_EditOutlined_HistoryOutlined_PlusOutlined_ReloadOutlined_StopOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>loadUsers(pagination.current, pagination.pageSize),\n                            children: \"刷新\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 353,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                columns: columns,\n                dataSource: users,\n                rowKey: \"id\",\n                loading: loading,\n                pagination: {\n                    ...pagination,\n                    showSizeChanger: true,\n                    showQuickJumper: true,\n                    showTotal: (total, range)=>\"第 \".concat(range[0], \"-\").concat(range[1], \" 条，共 \").concat(total, \" 条\")\n                },\n                onChange: (paginationInfo)=>{\n                    loadUsers(paginationInfo.current, paginationInfo.pageSize);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                title: \"创建新用户\",\n                open: createModalVisible,\n                onCancel: ()=>{\n                    setCreateModalVisible(false);\n                    createForm.resetFields();\n                },\n                footer: null,\n                width: 500,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    form: createForm,\n                    layout: \"vertical\",\n                    onFinish: handleCreateUser,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"用户名\",\n                            name: \"username\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入用户名'\n                                },\n                                {\n                                    min: 2,\n                                    message: '用户名至少2个字符'\n                                },\n                                {\n                                    max: 20,\n                                    message: '用户名最多20个字符'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                placeholder: \"请输入用户名\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"密码\",\n                            name: \"password\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入密码'\n                                },\n                                {\n                                    min: 6,\n                                    message: '密码至少6个字符'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"].Password, {\n                                placeholder: \"请输入密码\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            label: \"初始令牌数\",\n                            name: \"tokens\",\n                            initialValue: 1000,\n                            rules: [\n                                {\n                                    required: true,\n                                    message: '请输入初始令牌数'\n                                },\n                                {\n                                    type: 'number',\n                                    min: 0,\n                                    message: '令牌数不能为负数'\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                placeholder: \"请输入初始令牌数\",\n                                style: {\n                                    width: '100%'\n                                },\n                                min: 0,\n                                max: 999999\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                            className: \"mb-0 text-right\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        onClick: ()=>{\n                                            setCreateModalVisible(false);\n                                            createForm.resetFields();\n                                        },\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        type: \"primary\",\n                                        htmlType: \"submit\",\n                                        children: \"创建\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                    lineNumber: 399,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 389,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                title: \"为用户 \".concat(selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.username, \" 充值令牌\"),\n                open: rechargeModalVisible,\n                onCancel: ()=>{\n                    setRechargeModalVisible(false);\n                    rechargeForm.resetFields();\n                    setSelectedUser(null);\n                },\n                footer: null,\n                width: 500,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            type: \"secondary\",\n                            children: [\n                                \"当前余额: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    children: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.tokens) || 0\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 19\n                                }, this),\n                                \" 令牌\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        form: rechargeForm,\n                        layout: \"vertical\",\n                        onFinish: handleRecharge,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                label: \"充值数量\",\n                                name: \"amount\",\n                                rules: [\n                                    {\n                                        required: true,\n                                        message: '请输入充值数量'\n                                    },\n                                    {\n                                        type: 'number',\n                                        min: 1,\n                                        message: '充值数量至少为1'\n                                    }\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    placeholder: \"请输入充值数量\",\n                                    style: {\n                                        width: '100%'\n                                    },\n                                    min: 1,\n                                    max: 999999\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                label: \"充值说明\",\n                                name: \"description\",\n                                initialValue: \"管理员充值\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"].TextArea, {\n                                    placeholder: \"请输入充值说明（可选）\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                className: \"mb-0 text-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            onClick: ()=>{\n                                                setRechargeModalVisible(false);\n                                                rechargeForm.resetFields();\n                                                setSelectedUser(null);\n                                            },\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            type: \"primary\",\n                                            htmlType: \"submit\",\n                                            children: \"确认充值\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 461,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TokenUsageModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                visible: tokenUsageModalVisible,\n                onClose: ()=>{\n                    setTokenUsageModalVisible(false);\n                    setSelectedUser(null);\n                },\n                userId: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.id) || '',\n                username: (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.username) || ''\n            }, void 0, false, {\n                fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n                lineNumber: 528,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\项目文件夹\\\\XAP\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.tsx\",\n        lineNumber: 306,\n        columnNumber: 5\n    }, this);\n}\n_s(UserManagement, \"GgmXolWe9JxNniTtJQu7PVivIbc=\", false, function() {\n    return [\n        _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm,\n        _barrel_optimize_names_Badge_Button_Card_Col_Form_Input_InputNumber_Modal_Popconfirm_Row_Select_Space_Statistic_Table_Tag_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm\n    ];\n});\n_c = UserManagement;\nvar _c;\n$RefreshReg$(_c, \"UserManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/UserManagement.tsx\n"));

/***/ })

});