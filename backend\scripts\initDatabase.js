const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

async function initDatabase() {
  let connection;
  
  try {
    console.log('🚀 开始初始化数据库...');

    // 创建数据库连接（不指定数据库）
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      charset: 'utf8mb4'
    });

    console.log('✅ 数据库连接成功');

    // 读取SQL初始化脚本
    const sqlPath = path.join(__dirname, '../../database/init.sql');
    const sqlContent = await fs.readFile(sqlPath, 'utf8');

    // 分割SQL语句（按分号分割，忽略空行）
    const sqlStatements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📝 找到 ${sqlStatements.length} 条SQL语句`);

    // 执行每条SQL语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i];
      
      try {
        await connection.execute(statement);
        console.log(`✅ 执行SQL语句 ${i + 1}/${sqlStatements.length}`);
      } catch (error) {
        console.error(`❌ 执行SQL语句失败 ${i + 1}:`, error.message);
        console.error('SQL语句:', statement);
      }
    }

    console.log('🎉 数据库初始化完成！');
    console.log('📋 默认管理员账户:');
    console.log('   用户ID: 00001');
    console.log('   用户名: admin');
    console.log('   密码: admin123');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

module.exports = initDatabase;
